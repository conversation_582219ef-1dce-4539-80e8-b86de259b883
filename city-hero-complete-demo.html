<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市英雄区域重设计 - 完整演示</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        /* 基础重置和CSS变量系统 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 主色调 */
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --primary-light: #a5b4fc;
            --primary-dark: #3730a3;
            --primary-50: #eef2ff;
            --primary-100: #e0e7ff;
            --primary-200: #c7d2fe;
            --primary-300: #a5b4fc;
            --primary-400: #818cf8;
            --primary-500: #6366f1;
            --primary-600: #4f46e5;
            --primary-700: #4338ca;
            --primary-800: #3730a3;
            --primary-900: #312e81;

            /* 辅助色调 */
            --secondary-color: #64748b;
            --secondary-hover: #475569;
            --accent-color: #f59e0b;
            --success-color: #10b981;

            /* 中性色调 */
            --dark-color: #0f172a;
            --light-color: #f8fafc;
            --white: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --text-white: #ffffff;

            /* 边框和阴影 */
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 20px;
            --border-radius-full: 9999px;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);

            /* 间距系统 */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 0.75rem;
            --space-lg: 1rem;
            --space-xl: 1.25rem;
            --space-2xl: 1.5rem;
            --space-3xl: 2rem;
            --space-4xl: 3rem;
            --space-5xl: 4rem;

            /* 字体大小 */
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
            --text-5xl: 3rem;
            --text-6xl: 3.75rem;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--light-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* 演示页面样式 */
        .demo-header {
            background: var(--white);
            padding: var(--space-2xl) 0;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: var(--space-4xl);
        }

        .demo-title {
            font-size: var(--text-4xl);
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: var(--space-lg);
        }

        .demo-description {
            font-size: var(--text-lg);
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .variant-section {
            margin-bottom: var(--space-5xl);
            background: var(--white);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .variant-header {
            background: var(--primary-50);
            padding: var(--space-2xl);
            border-bottom: 1px solid var(--border-color);
        }

        .variant-title {
            font-size: var(--text-2xl);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--space-sm);
        }

        .variant-description {
            color: var(--text-secondary);
            font-size: var(--text-base);
        }

        /* 通用按钮样式 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            padding: var(--space-md) var(--space-2xl);
            border-radius: var(--border-radius);
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: var(--text-base);
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: var(--shadow-glow);
        }

        /* 动画关键帧 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* 导航菜单 */
        .demo-nav {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            padding: var(--space-lg);
            z-index: 1000;
        }

        .demo-nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .demo-nav li {
            margin-bottom: var(--space-sm);
        }

        .demo-nav a {
            display: block;
            padding: var(--space-sm) var(--space-lg);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            font-size: var(--text-sm);
            font-weight: 500;
        }

        .demo-nav a:hover {
            background: var(--primary-50);
            color: var(--primary-color);
        }

        /* 变体1：现代简约风格样式 */
        .city-hero-v1 {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-600) 100%);
            color: var(--white);
            padding: var(--space-5xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .city-hero-v1::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .city-hero-content-v1 {
            position: relative;
            z-index: 1;
            animation: fadeInUp 0.8s ease-out;
        }

        .city-badge-v1 {
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            background: rgba(255, 255, 255, 0.15);
            padding: var(--space-sm) var(--space-xl);
            border-radius: var(--border-radius-full);
            margin-bottom: var(--space-2xl);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: fadeInUp 0.8s ease-out 0.2s both;
            transition: all 0.3s ease;
        }

        .city-badge-v1:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .city-icon-v1 {
            font-size: 1.2rem;
        }

        .city-code-v1 {
            font-weight: 700;
            font-size: var(--text-sm);
            letter-spacing: 0.1em;
        }

        .city-title-v1 {
            font-size: var(--text-6xl);
            font-weight: 800;
            margin-bottom: var(--space-lg);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .city-subtitle-v1 {
            font-size: var(--text-xl);
            font-weight: 500;
            margin-bottom: var(--space-2xl);
            opacity: 0.9;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        .city-description-v1 {
            font-size: var(--text-lg);
            line-height: 1.6;
            margin-bottom: var(--space-4xl);
            opacity: 0.85;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        .city-stats-v1 {
            display: flex;
            justify-content: center;
            gap: var(--space-4xl);
            margin-bottom: var(--space-4xl);
            animation: fadeInUp 0.8s ease-out 1s both;
        }

        .stat-item-v1 {
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-item-v1:hover {
            transform: translateY(-5px);
        }

        .stat-number-v1 {
            font-size: var(--text-4xl);
            font-weight: 800;
            display: block;
            margin-bottom: var(--space-xs);
            background: linear-gradient(45deg, #ffffff, #e0e7ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label-v1 {
            font-size: var(--text-sm);
            opacity: 0.8;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .city-actions-v1 {
            display: flex;
            justify-content: center;
            gap: var(--space-lg);
            flex-wrap: wrap;
            animation: fadeInUp 0.8s ease-out 1.2s both;
        }
    </style>
</head>
<body>
    <!-- 导航菜单 -->
    <nav class="demo-nav">
        <ul>
            <li><a href="#variant1">变体1: 现代简约</a></li>
            <li><a href="#variant2">变体2: 卡片式</a></li>
            <li><a href="#variant3">变体3: 分屏布局</a></li>
            <li><a href="#variant4">变体4: 全屏沉浸</a></li>
            <li><a href="#variant5">变体5: 极简主义</a></li>
        </ul>
    </nav>

    <div class="demo-header">
        <div class="container">
            <h1 class="demo-title">城市英雄区域重设计</h1>
            <p class="demo-description">
                5种不同风格的设计变体，每种都保持与现有CSS变量系统的兼容性，
                并融入了fadeInUp动画、hover效果、渐变背景等您偏好的设计元素。
            </p>
        </div>
    </div>

    <!-- 变体1：现代简约风格 -->
    <div class="variant-section" id="variant1">
        <div class="variant-header">
            <h2 class="variant-title">变体1：现代简约风格</h2>
            <p class="variant-description">
                清洁的设计，强调内容层次，使用微妙的渐变和优雅的动画效果。
            </p>
        </div>

        <section class="city-hero-v1">
            <div class="container">
                <div class="city-hero-content-v1">
                    <div class="city-badge-v1">
                        <span class="city-icon-v1">📍</span>
                        <span class="city-code-v1">KL</span>
                    </div>
                    <h1 class="city-title-v1">吉隆坡</h1>
                    <h2 class="city-subtitle-v1">提供吉隆坡按摩、下水、B2B等真实服务信息</h2>
                    <p class="city-description-v1">
                        走马探花为您提供吉隆坡地区最全面的优质服务信息<br>
                        通过Telegram机器人获取详细联系方式和服务详情
                    </p>

                    <div class="city-stats-v1">
                        <div class="stat-item-v1">
                            <div class="stat-number-v1">15</div>
                            <div class="stat-label-v1">优质商家</div>
                        </div>
                        <div class="stat-item-v1">
                            <div class="stat-number-v1">3</div>
                            <div class="stat-label-v1">服务分类</div>
                        </div>
                        <div class="stat-item-v1">
                            <div class="stat-number-v1">24/7</div>
                            <div class="stat-label-v1">在线服务</div>
                        </div>
                    </div>

                    <div class="city-actions-v1">
                        <a href="#" class="btn btn-primary">
                            📱 联系机器人
                        </a>
                        <a href="#" class="btn btn-secondary">
                            🔍 搜索服务
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </div>

</body>
</html>
