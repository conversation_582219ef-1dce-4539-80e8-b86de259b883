<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>城市英雄区域重设计方案总结</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #6366f1;
            --primary-hover: #4f46e5;
            --primary-50: #eef2ff;
            --primary-100: #e0e7ff;
            --primary-600: #4f46e5;
            --secondary-color: #64748b;
            --accent-color: #f59e0b;
            --success-color: #10b981;
            --white: #ffffff;
            --light-color: #f8fafc;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --border-radius-lg: 16px;
            --border-radius-xl: 20px;
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --space-sm: 0.5rem;
            --space-lg: 1rem;
            --space-xl: 1.25rem;
            --space-2xl: 1.5rem;
            --space-3xl: 2rem;
            --space-4xl: 3rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--light-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-lg);
        }

        .header {
            background: var(--white);
            padding: var(--space-4xl) 0;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        .header h1 {
            font-size: var(--text-4xl);
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: var(--space-lg);
        }

        .header p {
            font-size: var(--text-lg);
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .variants-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--space-3xl);
            padding: var(--space-4xl) 0;
        }

        .variant-card {
            background: var(--white);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .variant-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl);
        }

        .variant-preview {
            height: 200px;
            position: relative;
            overflow: hidden;
        }

        .variant-content {
            padding: var(--space-2xl);
        }

        .variant-title {
            font-size: var(--text-xl);
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: var(--space-sm);
        }

        .variant-description {
            color: var(--text-secondary);
            margin-bottom: var(--space-lg);
            line-height: 1.6;
        }

        .variant-features {
            list-style: none;
            margin-bottom: var(--space-lg);
        }

        .variant-features li {
            padding: var(--space-sm) 0;
            color: var(--text-muted);
            font-size: var(--text-sm);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
        }

        .variant-features li::before {
            content: "✓";
            color: var(--success-color);
            font-weight: bold;
        }

        .variant-button {
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            padding: var(--space-sm) var(--space-xl);
            background: var(--primary-color);
            color: var(--white);
            text-decoration: none;
            border-radius: var(--border-radius);
            font-weight: 600;
            font-size: var(--text-sm);
            transition: all 0.3s ease;
        }

        .variant-button:hover {
            background: var(--primary-hover);
            transform: translateY(-2px);
        }

        /* 变体预览样式 */
        .preview-v1 {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-600) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            text-align: center;
        }

        .preview-v2 {
            background: linear-gradient(135deg, var(--primary-50) 0%, var(--light-color) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-lg);
        }

        .preview-card {
            background: var(--white);
            padding: var(--space-xl);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            text-align: center;
            width: 80%;
        }

        .preview-v3 {
            background: var(--white);
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-lg);
            padding: var(--space-lg);
        }

        .preview-v4 {
            background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            position: relative;
            overflow: hidden;
        }

        .preview-v4::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="80" r="2" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.5;
        }

        .preview-v5 {
            background: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid var(--primary-color);
        }

        .preview-content {
            text-align: center;
            padding: var(--space-lg);
        }

        .preview-title {
            font-size: var(--text-lg);
            font-weight: 700;
            margin-bottom: var(--space-sm);
        }

        .preview-subtitle {
            font-size: var(--text-sm);
            opacity: 0.8;
        }

        .mini-stats {
            display: flex;
            gap: var(--space-lg);
            justify-content: center;
            margin-top: var(--space-lg);
        }

        .mini-stat {
            text-align: center;
        }

        .mini-stat-number {
            font-weight: 800;
            font-size: var(--text-lg);
        }

        .mini-stat-label {
            font-size: var(--text-sm);
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .variants-grid {
                grid-template-columns: 1fr;
                gap: var(--space-2xl);
                padding: var(--space-2xl) 0;
            }
            
            .preview-v3 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="container">
            <h1>城市英雄区域重设计方案</h1>
            <p>5种不同风格的设计变体，每种都保持与现有CSS变量系统的兼容性，并融入了您偏好的设计元素。</p>
        </div>
    </div>

    <div class="container">
        <div class="variants-grid">
            <!-- 变体1：现代简约风格 -->
            <div class="variant-card">
                <div class="variant-preview preview-v1">
                    <div class="preview-content">
                        <div class="preview-title">吉隆坡</div>
                        <div class="preview-subtitle">现代简约设计</div>
                        <div class="mini-stats">
                            <div class="mini-stat">
                                <div class="mini-stat-number">15</div>
                                <div class="mini-stat-label">商家</div>
                            </div>
                            <div class="mini-stat">
                                <div class="mini-stat-number">3</div>
                                <div class="mini-stat-label">分类</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="variant-content">
                    <h3 class="variant-title">变体1：现代简约风格</h3>
                    <p class="variant-description">
                        清洁的设计，强调内容层次，使用微妙的渐变和优雅的fadeInUp动画效果。
                    </p>
                    <ul class="variant-features">
                        <li>渐变背景与径向光效</li>
                        <li>分层fadeInUp动画（0.2s递增延迟）</li>
                        <li>悬停时translateY(-5px)效果</li>
                        <li>渐变文字效果</li>
                        <li>响应式网格布局</li>
                    </ul>
                    <a href="#" class="variant-button">查看详细代码 →</a>
                </div>
            </div>

            <!-- 变体2：卡片式设计 -->
            <div class="variant-card">
                <div class="variant-preview preview-v2">
                    <div class="preview-card">
                        <div class="preview-title" style="color: var(--primary-color);">吉隆坡</div>
                        <div class="preview-subtitle" style="color: var(--text-secondary);">卡片式设计</div>
                        <div class="mini-stats">
                            <div class="mini-stat">
                                <div class="mini-stat-number" style="color: var(--primary-color);">15</div>
                                <div class="mini-stat-label">商家</div>
                            </div>
                            <div class="mini-stat">
                                <div class="mini-stat-number" style="color: var(--primary-color);">3</div>
                                <div class="mini-stat-label">分类</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="variant-content">
                    <h3 class="variant-title">变体2：卡片式设计</h3>
                    <p class="variant-description">
                        将内容包装在优雅的卡片中，营造层次感和深度，适合现代Web应用。
                    </p>
                    <ul class="variant-features">
                        <li>白色卡片容器与阴影效果</li>
                        <li>彩色顶部边框装饰</li>
                        <li>scaleIn进入动画</li>
                        <li>统计卡片网格布局</li>
                        <li>悬停时scale(1.05)效果</li>
                    </ul>
                    <a href="#" class="variant-button">查看详细代码 →</a>
                </div>
            </div>

            <!-- 变体3：分屏布局设计 -->
            <div class="variant-card">
                <div class="variant-preview preview-v3">
                    <div style="padding: var(--space-lg);">
                        <div class="preview-title" style="color: var(--text-primary); text-align: left;">吉隆坡</div>
                        <div class="preview-subtitle" style="color: var(--text-secondary); text-align: left;">分屏布局</div>
                    </div>
                    <div style="background: var(--primary-50); padding: var(--space-lg); border-radius: var(--border-radius);">
                        <div style="text-align: center;">
                            <div style="font-weight: 700; color: var(--primary-color);">统计面板</div>
                            <div style="font-size: var(--text-sm); color: var(--text-muted);">数据展示</div>
                        </div>
                    </div>
                </div>
                <div class="variant-content">
                    <h3 class="variant-title">变体3：分屏布局设计</h3>
                    <p class="variant-description">
                        左右分屏布局，左侧内容右侧统计，营造现代感和专业感。
                    </p>
                    <ul class="variant-features">
                        <li>左右分屏网格布局</li>
                        <li>slideInLeft/Right动画</li>
                        <li>圆形统计图标</li>
                        <li>独立统计面板</li>
                        <li>悬停时translateX(10px)</li>
                    </ul>
                    <a href="#" class="variant-button">查看详细代码 →</a>
                </div>
            </div>

            <!-- 变体4：全屏沉浸式 -->
            <div class="variant-card">
                <div class="variant-preview preview-v4">
                    <div class="preview-content" style="position: relative; z-index: 1;">
                        <div class="preview-title">吉隆坡</div>
                        <div class="preview-subtitle">全屏沉浸式</div>
                        <div class="mini-stats">
                            <div class="mini-stat">
                                <div class="mini-stat-number">15</div>
                                <div class="mini-stat-label">商家</div>
                            </div>
                            <div class="mini-stat">
                                <div class="mini-stat-number">3</div>
                                <div class="mini-stat-label">分类</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="variant-content">
                    <h3 class="variant-title">变体4：全屏沉浸式</h3>
                    <p class="variant-description">
                        全屏高度设计，多彩渐变背景，营造沉浸式体验。
                    </p>
                    <ul class="variant-features">
                        <li>100vh全屏高度</li>
                        <li>多彩渐变背景</li>
                        <li>粒子装饰效果</li>
                        <li>视差滚动动画</li>
                        <li>浮动统计卡片</li>
                    </ul>
                    <a href="#" class="variant-button">查看详细代码 →</a>
                </div>
            </div>

            <!-- 变体5：极简主义 -->
            <div class="variant-card">
                <div class="variant-preview preview-v5">
                    <div class="preview-content">
                        <div class="preview-title" style="color: var(--primary-color);">吉隆坡</div>
                        <div class="preview-subtitle" style="color: var(--text-secondary);">极简主义</div>
                        <div style="margin-top: var(--space-lg); padding-top: var(--space-lg); border-top: 1px solid var(--border-color);">
                            <div style="font-size: var(--text-sm); color: var(--text-muted);">15 商家 • 3 分类</div>
                        </div>
                    </div>
                </div>
                <div class="variant-content">
                    <h3 class="variant-title">变体5：极简主义</h3>
                    <p class="variant-description">
                        去除多余装饰，专注内容本身，使用线条和留白营造优雅感。
                    </p>
                    <ul class="variant-features">
                        <li>纯白背景与线条边框</li>
                        <li>大量留白设计</li>
                        <li>简洁的排版层次</li>
                        <li>微妙的悬停效果</li>
                        <li>内联统计显示</li>
                    </ul>
                    <a href="#" class="variant-button">查看详细代码 →</a>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
