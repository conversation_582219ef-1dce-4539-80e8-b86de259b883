<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>樱花主题城市英雄区域 - 完整演示</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 樱花粉色主题调色板 */
            --primary-color: #ff69b4;        /* 樱花粉主色 */
            --primary-hover: #ff1493;        /* 深粉色悬停 */
            --primary-light: #ffb6c1;        /* 浅樱花粉 */
            --primary-dark: #dc143c;         /* 深樱花红 */
            --primary-50: #fef7f7;           /* 极浅粉色背景 */
            --primary-100: #fce7e7;          /* 浅粉色背景 */
            --primary-200: #f9c2c2;          /* 粉色装饰 */
            --primary-300: #f59bb6;          /* 中等粉色 */
            --primary-400: #ff69b4;          /* 标准樱花粉 */
            --primary-500: #ff1493;          /* 鲜艳粉色 */
            --primary-600: #e91e63;          /* 深粉色 */
            --primary-700: #c2185b;          /* 更深粉色 */
            --primary-800: #ad1457;          /* 深樱花色 */
            --primary-900: #880e4f;          /* 最深粉色 */
            
            /* 樱花渐变色 */
            --sakura-gradient: linear-gradient(135deg, #ff69b4 0%, #ffb6c1 50%, #ffc0cb 100%);
            --sakura-light-gradient: linear-gradient(135deg, #fef7f7 0%, #fce7e7 100%);
            --sakura-glow: 0 0 20px rgba(255, 105, 180, 0.3);
            
            /* 其他颜色 */
            --secondary-color: #64748b;
            --accent-color: #ffc0cb;          /* 樱花粉辅助色 */
            --success-color: #10b981;
            --white: #ffffff;
            --light-color: #f8fafc;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --text-white: #ffffff;

            /* 边框和阴影 */
            --border-color: #e2e8f0;
            --border-radius: 8px;
            --border-radius-md: 12px;
            --border-radius-lg: 16px;
            --border-radius-xl: 20px;
            --border-radius-full: 9999px;
            --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

            /* 间距系统 */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 0.75rem;
            --space-lg: 1rem;
            --space-xl: 1.25rem;
            --space-2xl: 1.5rem;
            --space-3xl: 2rem;
            --space-4xl: 3rem;
            --space-5xl: 4rem;

            /* 字体大小 */
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
            --text-5xl: 3rem;
            --text-6xl: 3.75rem;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--light-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-lg);
        }

        /* 演示页面样式 */
        .demo-header {
            background: var(--sakura-light-gradient);
            padding: var(--space-4xl) 0;
            text-align: center;
            border-bottom: 1px solid var(--primary-200);
            position: relative;
            overflow: hidden;
        }

        .demo-header::before {
            content: '🌸';
            position: absolute;
            top: 20px;
            left: 20px;
            font-size: 2rem;
            opacity: 0.3;
            animation: float 4s ease-in-out infinite;
        }

        .demo-header::after {
            content: '🌸';
            position: absolute;
            bottom: 20px;
            right: 20px;
            font-size: 1.5rem;
            opacity: 0.4;
            animation: float 3s ease-in-out infinite reverse;
        }

        .demo-title {
            font-size: var(--text-4xl);
            font-weight: 800;
            background: var(--sakura-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--space-lg);
            position: relative;
            z-index: 1;
        }

        .demo-subtitle {
            font-size: var(--text-lg);
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        /* 通用按钮样式 - 樱花主题 */
        .btn {
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            padding: var(--space-md) var(--space-2xl);
            border-radius: var(--border-radius);
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: var(--text-base);
        }

        .btn-primary {
            background: var(--sakura-gradient);
            color: var(--white);
            box-shadow: var(--sakura-glow);
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-600) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: var(--white);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .btn-outline {
            background: transparent;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn-outline:hover {
            background: var(--primary-color);
            color: var(--white);
            transform: translateY(-2px);
            box-shadow: var(--sakura-glow);
        }

        /* 动画关键帧 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }

        @keyframes sakuraPetal {
            0% { transform: translateY(-10px) rotate(0deg); opacity: 1; }
            100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
        }

        /* 樱花花瓣装饰 */
        .sakura-petals {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .sakura-petal {
            position: absolute;
            color: var(--primary-light);
            font-size: 1rem;
            animation: sakuraPetal 8s linear infinite;
        }

        .sakura-petal:nth-child(1) { left: 10%; animation-delay: 0s; }
        .sakura-petal:nth-child(2) { left: 20%; animation-delay: 2s; }
        .sakura-petal:nth-child(3) { left: 30%; animation-delay: 4s; }
        .sakura-petal:nth-child(4) { left: 40%; animation-delay: 1s; }
        .sakura-petal:nth-child(5) { left: 50%; animation-delay: 3s; }
        .sakura-petal:nth-child(6) { left: 60%; animation-delay: 5s; }
        .sakura-petal:nth-child(7) { left: 70%; animation-delay: 1.5s; }
        .sakura-petal:nth-child(8) { left: 80%; animation-delay: 3.5s; }
        .sakura-petal:nth-child(9) { left: 90%; animation-delay: 2.5s; }

        /* 导航菜单 - 樱花主题 */
        .demo-nav {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg);
            padding: var(--space-lg);
            z-index: 1000;
            border: 2px solid var(--primary-100);
        }

        .demo-nav ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .demo-nav li {
            margin-bottom: var(--space-sm);
        }

        .demo-nav a {
            display: block;
            padding: var(--space-sm) var(--space-lg);
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            font-size: var(--text-sm);
            font-weight: 500;
        }

        .demo-nav a:hover {
            background: var(--primary-50);
            color: var(--primary-color);
        }

        /* 变体1：樱花现代简约风格样式 */
        .variant-section {
            margin-bottom: var(--space-5xl);
        }

        .variant-header {
            background: var(--sakura-light-gradient);
            padding: var(--space-4xl) 0;
            text-align: center;
            position: relative;
        }

        .variant-header::before {
            content: '🌸';
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 2rem;
            opacity: 0.2;
        }

        .city-hero-v1 {
            background: var(--sakura-gradient);
            color: var(--white);
            padding: var(--space-5xl) 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .city-hero-v1::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.15) 0%, transparent 50%),
                        radial-gradient(circle at 70% 80%, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .city-hero-v1::after {
            content: '🌸';
            position: absolute;
            top: 30px;
            right: 50px;
            font-size: 3rem;
            opacity: 0.2;
            animation: float 4s ease-in-out infinite;
        }

        .city-hero-content-v1 {
            position: relative;
            z-index: 1;
            animation: fadeInUp 0.8s ease-out;
        }

        .city-badge-v1 {
            display: inline-flex;
            align-items: center;
            gap: var(--space-sm);
            background: rgba(255, 255, 255, 0.2);
            padding: var(--space-sm) var(--space-xl);
            border-radius: var(--border-radius-full);
            margin-bottom: var(--space-2xl);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            animation: fadeInUp 0.8s ease-out 0.2s both;
            transition: all 0.3s ease;
        }

        .city-badge-v1:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.3);
            background: rgba(255, 255, 255, 0.3);
        }

        .city-icon-v1 {
            font-size: 1.2rem;
        }

        .city-code-v1 {
            font-weight: 700;
            font-size: var(--text-sm);
            letter-spacing: 0.1em;
        }

        .city-title-v1 {
            font-size: var(--text-6xl);
            font-weight: 800;
            margin-bottom: var(--space-lg);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            animation: fadeInUp 0.8s ease-out 0.4s both;
        }

        .city-subtitle-v1 {
            font-size: var(--text-xl);
            font-weight: 500;
            margin-bottom: var(--space-2xl);
            opacity: 0.95;
            max-width: 700px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }

        .city-description-v1 {
            font-size: var(--text-lg);
            line-height: 1.6;
            margin-bottom: var(--space-4xl);
            opacity: 0.9;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }

        .city-stats-v1 {
            display: flex;
            justify-content: center;
            gap: var(--space-4xl);
            margin-bottom: var(--space-4xl);
            animation: fadeInUp 0.8s ease-out 1s both;
        }

        .stat-item-v1 {
            text-align: center;
            transition: transform 0.3s ease;
            position: relative;
        }

        .stat-item-v1:hover {
            transform: translateY(-8px);
        }

        .stat-item-v1::before {
            content: '🌸';
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stat-item-v1:hover::before {
            opacity: 0.7;
        }

        .stat-number-v1 {
            font-size: var(--text-4xl);
            font-weight: 800;
            display: block;
            margin-bottom: var(--space-xs);
            background: linear-gradient(45deg, #ffffff, #ffc0cb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .stat-label-v1 {
            font-size: var(--text-sm);
            opacity: 0.85;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .city-actions-v1 {
            display: flex;
            justify-content: center;
            gap: var(--space-lg);
            flex-wrap: wrap;
            animation: fadeInUp 0.8s ease-out 1.2s both;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .demo-title {
                font-size: var(--text-3xl);
            }

            .city-hero-v1 {
                padding: var(--space-4xl) 0;
            }

            .city-hero-v1::after {
                font-size: 2rem;
                top: 20px;
                right: 20px;
            }

            .city-title-v1 {
                font-size: var(--text-4xl);
            }

            .city-subtitle-v1 {
                font-size: var(--text-lg);
            }

            .city-stats-v1 {
                gap: var(--space-2xl);
                flex-wrap: wrap;
            }

            .stat-number-v1 {
                font-size: var(--text-3xl);
            }

            .city-actions-v1 {
                flex-direction: column;
                align-items: center;
            }

            .city-actions-v1 .btn {
                width: 200px;
            }

            .sakura-petals {
                display: none; /* 移动端隐藏花瓣动画以提升性能 */
            }
        }
    </style>
</head>
<body>
    <!-- 樱花花瓣装饰 -->
    <div class="sakura-petals">
        <div class="sakura-petal">🌸</div>
        <div class="sakura-petal">🌸</div>
        <div class="sakura-petal">🌸</div>
        <div class="sakura-petal">🌸</div>
        <div class="sakura-petal">🌸</div>
        <div class="sakura-petal">🌸</div>
        <div class="sakura-petal">🌸</div>
        <div class="sakura-petal">🌸</div>
        <div class="sakura-petal">🌸</div>
    </div>

    <!-- 导航菜单 -->
    <nav class="demo-nav">
        <ul>
            <li><a href="#variant1">🌸 现代简约</a></li>
            <li><a href="#variant2">🌸 卡片式</a></li>
            <li><a href="#variant3">🌸 分屏布局</a></li>
            <li><a href="#variant4">🌸 全屏沉浸</a></li>
            <li><a href="#variant5">🌸 极简主义</a></li>
        </ul>
    </nav>

    <div class="demo-header">
        <div class="container">
            <h1 class="demo-title">🌸 樱花主题城市英雄区域</h1>
            <p class="demo-subtitle">
                5种优雅的樱花粉色设计变体，完美配合您的樱花logo主题，
                保持现代感的同时融入温柔浪漫的樱花元素。
            </p>
        </div>
    </div>

    <!-- 变体1：樱花现代简约风格 -->
    <section class="variant-section" id="variant1">
        <div class="variant-header">
            <div class="container">
                <h2 style="color: var(--primary-color); font-size: var(--text-2xl); font-weight: 700; margin-bottom: var(--space-sm);">
                    🌸 变体1：樱花现代简约风格
                </h2>
                <p style="color: var(--text-secondary); margin-bottom: var(--space-3xl);">
                    清洁的设计，强调内容层次，使用樱花渐变和优雅的动画效果。
                </p>
            </div>
        </div>

        <section class="city-hero-v1">
            <div class="container">
                <div class="city-hero-content-v1">
                    <div class="city-badge-v1">
                        <span class="city-icon-v1">📍</span>
                        <span class="city-code-v1">KL</span>
                    </div>
                    <h1 class="city-title-v1">吉隆坡</h1>
                    <h2 class="city-subtitle-v1">提供吉隆坡按摩、下水、B2B等真实服务信息</h2>
                    <p class="city-description-v1">
                        走马探花为您提供吉隆坡地区最全面的优质服务信息<br>
                        通过Telegram机器人获取详细联系方式和服务详情
                    </p>

                    <div class="city-stats-v1">
                        <div class="stat-item-v1">
                            <div class="stat-number-v1">15</div>
                            <div class="stat-label-v1">优质商家</div>
                        </div>
                        <div class="stat-item-v1">
                            <div class="stat-number-v1">3</div>
                            <div class="stat-label-v1">服务分类</div>
                        </div>
                        <div class="stat-item-v1">
                            <div class="stat-number-v1">24/7</div>
                            <div class="stat-label-v1">在线服务</div>
                        </div>
                    </div>

                    <div class="city-actions-v1">
                        <a href="#" class="btn btn-primary">
                            📱 联系机器人
                        </a>
                        <a href="#" class="btn btn-secondary">
                            🔍 搜索服务
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </section>

</body>
</html>
