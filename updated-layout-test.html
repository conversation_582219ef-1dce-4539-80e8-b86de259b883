<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 更新后布局测试 - 樱花分屏布局</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            padding: 20px;
        }

        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .test-header {
            background: linear-gradient(135deg, #ff69b4, #ffb6c1);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .test-subtitle {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .changes-summary {
            background: #e7f3ff;
            padding: 25px;
            margin: 20px;
            border-radius: 12px;
            border-left: 4px solid #ff69b4;
        }

        .changes-summary h3 {
            color: #ff1493;
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 15px;
        }

        .change-item {
            margin-bottom: 12px;
            padding-left: 20px;
            position: relative;
        }

        .change-item::before {
            content: "✅";
            position: absolute;
            left: 0;
            top: 0;
        }

        .preview-section {
            padding: 30px;
        }

        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .device-preview {
            border: 3px solid #e2e8f0;
            border-radius: 20px;
            overflow: hidden;
            background: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .device-header {
            background: #64748b;
            color: white;
            padding: 12px 20px;
            font-weight: 600;
            font-size: 0.9rem;
            text-align: center;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .device-size {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .device-screen {
            height: 500px;
            overflow: hidden;
            background: white;
            position: relative;
        }

        .device-screen iframe {
            width: 100%;
            height: 100%;
            border: none;
            transform-origin: top left;
        }

        .desktop-screen iframe {
            transform: scale(0.25);
            width: 400%;
            height: 400%;
        }

        .tablet-screen iframe {
            transform: scale(0.4);
            width: 250%;
            height: 250%;
        }

        .mobile-screen iframe {
            transform: scale(0.6);
            width: 167%;
            height: 167%;
        }

        .small-screen iframe {
            transform: scale(0.8);
            width: 125%;
            height: 125%;
        }

        .layout-comparison {
            background: #f8fafc;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
        }

        .layout-comparison h3 {
            color: #ff69b4;
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .layout-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #fce7e7;
            text-align: center;
        }

        .layout-item h4 {
            color: #ff1493;
            font-weight: 600;
            margin-bottom: 12px;
            font-size: 1.1rem;
        }

        .layout-visual {
            background: #fef7f7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 12px;
            font-size: 0.9rem;
            color: #64748b;
        }

        .stats-layout {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 10px 0;
        }

        .mini-stat {
            width: 20px;
            height: 20px;
            background: #ff69b4;
            border-radius: 50%;
            color: white;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }

        .stats-vertical {
            flex-direction: column;
            gap: 8px;
        }

        .test-links {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 30px;
        }

        .test-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: #ff69b4;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .test-link:hover {
            background: #ff1493;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 105, 180, 0.3);
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">📱 更新后布局测试</h1>
            <p class="test-subtitle">樱花分屏布局 - 修改验证</p>
        </div>

        <div class="changes-summary">
            <h3>🔄 本次修改内容</h3>
            <div class="change-item">删除了 <span class="highlight">"🔍 搜索服务"</span> 按钮，只保留 <span class="highlight">"📱 联系机器人"</span> 按钮</div>
            <div class="change-item">平板端和移动端的统计项从 <span class="highlight">垂直排列</span> 改为 <span class="highlight">水平排列</span></div>
            <div class="change-item">优化了不同屏幕尺寸下统计圆圈和文字的尺寸</div>
            <div class="change-item">确保移动端单个按钮居中显示</div>
            <div class="change-item">保持桌面端原有的垂直统计布局不变</div>
        </div>

        <div class="layout-comparison">
            <h3>📐 不同屏幕尺寸下的统计布局</h3>
            <div class="comparison-grid">
                <div class="layout-item">
                    <h4>🖥️ 桌面端 (1025px+)</h4>
                    <div class="layout-visual">
                        <div class="stats-layout stats-vertical">
                            <div class="mini-stat">15</div>
                            <div class="mini-stat">3</div>
                            <div class="mini-stat">24</div>
                        </div>
                        <strong>垂直排列</strong><br>
                        圆圈: 80px | 文字: 18px
                    </div>
                </div>

                <div class="layout-item">
                    <h4>📱 平板端 (769px-1024px)</h4>
                    <div class="layout-visual">
                        <div class="stats-layout">
                            <div class="mini-stat">15</div>
                            <div class="mini-stat">3</div>
                            <div class="mini-stat">24</div>
                        </div>
                        <strong>水平排列</strong><br>
                        圆圈: 65px | 文字: 14px
                    </div>
                </div>

                <div class="layout-item">
                    <h4>📱 移动端 (481px-768px)</h4>
                    <div class="layout-visual">
                        <div class="stats-layout">
                            <div class="mini-stat">15</div>
                            <div class="mini-stat">3</div>
                            <div class="mini-stat">24</div>
                        </div>
                        <strong>水平排列</strong><br>
                        圆圈: 60px | 文字: 14px
                    </div>
                </div>

                <div class="layout-item">
                    <h4>📱 小屏幕 (≤480px)</h4>
                    <div class="layout-visual">
                        <div class="stats-layout">
                            <div class="mini-stat" style="width: 16px; height: 16px; font-size: 0.6rem;">15</div>
                            <div class="mini-stat" style="width: 16px; height: 16px; font-size: 0.6rem;">3</div>
                            <div class="mini-stat" style="width: 16px; height: 16px; font-size: 0.6rem;">24</div>
                        </div>
                        <strong>水平排列</strong><br>
                        圆圈: 50px | 文字: 12px
                    </div>
                </div>
            </div>
        </div>

        <div class="preview-section">
            <div class="preview-grid">
                <div class="device-preview">
                    <div class="device-header">
                        <span>🖥️ 桌面端</span>
                        <span class="device-size">1200px+</span>
                    </div>
                    <div class="device-screen desktop-screen">
                        <iframe src="final-sakura-split-layout.html"></iframe>
                    </div>
                </div>

                <div class="device-preview">
                    <div class="device-header">
                        <span>📱 平板端</span>
                        <span class="device-size">768px-1024px</span>
                    </div>
                    <div class="device-screen tablet-screen">
                        <iframe src="final-sakura-split-layout.html"></iframe>
                    </div>
                </div>

                <div class="device-preview">
                    <div class="device-header">
                        <span>📱 移动端</span>
                        <span class="device-size">481px-768px</span>
                    </div>
                    <div class="device-screen mobile-screen">
                        <iframe src="final-sakura-split-layout.html"></iframe>
                    </div>
                </div>

                <div class="device-preview">
                    <div class="device-header">
                        <span>📱 小屏幕</span>
                        <span class="device-size">≤480px</span>
                    </div>
                    <div class="device-screen small-screen">
                        <iframe src="final-sakura-split-layout.html"></iframe>
                    </div>
                </div>
            </div>

            <div class="test-links">
                <a href="final-sakura-split-layout.html" class="test-link" target="_blank">
                    🌸 打开完整页面
                </a>
                <a href="final-sakura-split-layout.html" class="test-link" target="_blank" onclick="window.open(this.href, '_blank', 'width=375,height=667'); return false;">
                    📱 移动端窗口测试
                </a>
                <a href="final-sakura-split-layout.html" class="test-link" target="_blank" onclick="window.open(this.href, '_blank', 'width=768,height=1024'); return false;">
                    📱 平板端窗口测试
                </a>
                <a href="final-sakura-split-layout.html" class="test-link" target="_blank" onclick="window.open(this.href, '_blank', 'width=320,height=568'); return false;">
                    📱 小屏幕窗口测试
                </a>
            </div>
        </div>
    </div>
</body>
</html>
