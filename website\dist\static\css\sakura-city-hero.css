/* 樱花主题城市英雄区域样式 */

/* 樱花主题CSS变量 */
:root {
    /* 樱花粉色主题调色板 */
    --sakura-primary: #ff69b4;
    --sakura-primary-hover: #ff1493;
    --sakura-primary-light: #ffb6c1;
    --sakura-primary-dark: #dc143c;
    --sakura-50: #fef7f7;
    --sakura-100: #fce7e7;
    --sakura-200: #f9c2c2;
    --sakura-300: #f59bb6;
    --sakura-400: #ff69b4;
    --sakura-500: #ff1493;
    --sakura-600: #e91e63;
    --sakura-700: #c2185b;
    --sakura-800: #ad1457;
    --sakura-900: #880e4f;
    
    /* 樱花渐变色 */
    --sakura-gradient: linear-gradient(135deg, #ff69b4 0%, #ffb6c1 50%, #ffc0cb 100%);
    --sakura-light-gradient: linear-gradient(135deg, #fef7f7 0%, #fce7e7 100%);
    --sakura-glow: 0 0 20px rgba(255, 105, 180, 0.3);
}

/* 动画关键帧 */
@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-10px) rotate(5deg);
    }
}

/* 樱花分屏布局主要样式 */
.city-hero {
    background: var(--white, #ffffff);
    padding: var(--space-5xl, 4rem) 0;
    min-height: 80vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.city-hero::before {
    content: '🌸';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 8rem;
    opacity: 0.05;
    z-index: 0;
    animation: float 6s ease-in-out infinite;
}

.city-hero-grid {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--space-5xl, 4rem);
    align-items: center;
    position: relative;
    z-index: 1;
}

.city-content {
    animation: slideInLeft 0.8s ease-out;
}

.city-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm, 0.5rem);
    background: var(--sakura-100);
    color: var(--sakura-primary);
    padding: var(--space-sm, 0.5rem) var(--space-lg, 1rem);
    border-radius: var(--border-radius-full, 9999px);
    margin-bottom: var(--space-2xl, 1.5rem);
    font-weight: 600;
    transition: all 0.3s ease;
}

.city-badge:hover {
    transform: translateX(10px);
    background: var(--sakura-primary);
    color: var(--white, #ffffff);
    box-shadow: var(--sakura-glow);
}

.city-icon {
    font-size: 1.2rem;
}

.city-code {
    font-weight: 700;
    font-size: var(--text-sm, 0.875rem);
    letter-spacing: 0.1em;
}

.city-title {
    font-size: var(--text-6xl, 3.75rem);
    font-weight: 800;
    margin-bottom: var(--space-lg, 1rem);
    color: var(--text-primary, #0f172a);
}

.city-subtitle {
    font-size: var(--text-2xl, 1.5rem);
    font-weight: 600;
    color: var(--text-secondary, #64748b);
    margin-bottom: var(--space-2xl, 1.5rem);
    line-height: 1.4;
}

.city-description {
    font-size: var(--text-lg, 1.125rem);
    line-height: 1.6;
    color: var(--text-muted, #94a3b8);
    margin-bottom: var(--space-4xl, 3rem);
}

.city-actions {
    display: flex;
    gap: var(--space-lg, 1rem);
}

.stats-panel {
    background: var(--white, #ffffff);
    border-radius: var(--border-radius-xl, 20px);
    padding: var(--space-4xl, 3rem);
    box-shadow: var(--shadow-xl, 0 20px 25px -5px rgba(0, 0, 0, 0.1));
    border: 2px solid var(--sakura-100);
    animation: slideInRight 0.8s ease-out 0.4s both;
    position: relative;
}

.stats-panel::before {
    content: '🌸';
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 1.2rem;
    opacity: 0.4;
    animation: float 4s ease-in-out infinite;
}

.stats-header {
    text-align: center;
    margin-bottom: var(--space-3xl, 2rem);
}

.stats-header h3 {
    font-size: var(--text-2xl, 1.5rem);
    font-weight: 700;
    color: var(--sakura-primary);
    margin-bottom: var(--space-sm, 0.5rem);
}

.stats-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-3xl, 2rem);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--space-lg, 1rem);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateX(10px);
}

.stat-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--sakura-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white, #ffffff);
    font-weight: 800;
    font-size: var(--text-lg, 1.125rem);
    box-shadow: var(--sakura-glow);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.stat-item:hover .stat-circle {
    transform: scale(1.1);
    box-shadow: 0 10px 30px rgba(255, 105, 180, 0.4);
}

.stat-label {
    font-size: var(--text-lg, 1.125rem);
    font-weight: 600;
    color: var(--text-primary, #0f172a);
}

/* 按钮样式更新 */
.btn-primary {
    background: var(--sakura-gradient) !important;
    color: var(--white, #ffffff) !important;
    box-shadow: var(--sakura-glow) !important;
    border: none !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--sakura-primary-hover) 0%, var(--sakura-600) 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4) !important;
}

/* 移动端响应式设计 */
@media (max-width: 768px) {
    .city-hero {
        padding: var(--space-4xl, 3rem) 0;
        min-height: auto;
    }

    .city-hero::before {
        font-size: 4rem;
        opacity: 0.03;
    }

    .city-hero-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4xl, 3rem);
    }

    .city-content {
        text-align: center;
        animation: fadeInUp 0.8s ease-out;
    }

    .city-badge {
        margin-bottom: var(--space-xl, 1.25rem);
    }

    .city-title {
        font-size: var(--text-4xl, 2.25rem);
        margin-bottom: var(--space-lg, 1rem);
    }

    .city-subtitle {
        font-size: var(--text-xl, 1.25rem);
        margin-bottom: var(--space-xl, 1.25rem);
    }

    .city-description {
        font-size: var(--text-base, 1rem);
        margin-bottom: var(--space-3xl, 2rem);
    }

    .city-actions {
        justify-content: center;
    }

    .city-actions .btn {
        width: 100%;
        max-width: 280px;
        justify-content: center;
    }

    .stats-panel {
        padding: var(--space-3xl, 2rem);
        animation: fadeInUp 0.8s ease-out 0.2s both;
    }

    .stats-panel::before {
        top: 10px;
        right: 10px;
        font-size: 1rem;
    }

    .stats-header h3 {
        font-size: var(--text-xl, 1.25rem);
    }

    .stats-list {
        flex-direction: row;
        justify-content: space-between;
        gap: var(--space-lg, 1rem);
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm, 0.5rem);
        flex: 1;
        min-width: 0;
    }

    .stat-item:hover {
        transform: translateY(-5px);
    }

    .stat-circle {
        width: 60px;
        height: 60px;
        font-size: var(--text-sm, 0.875rem);
        margin: 0 auto;
    }

    .stat-label {
        font-size: var(--text-sm, 0.875rem);
        line-height: 1.3;
    }
}

/* 平板端适配 */
@media (max-width: 1024px) and (min-width: 769px) {
    .city-hero-grid {
        grid-template-columns: 1fr 350px;
        gap: var(--space-4xl, 3rem);
    }

    .city-title {
        font-size: var(--text-5xl, 3rem);
    }

    .stats-panel {
        padding: var(--space-3xl, 2rem);
    }

    .stats-list {
        flex-direction: row;
        justify-content: space-between;
        gap: var(--space-md, 0.75rem);
    }

    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: var(--space-sm, 0.5rem);
        flex: 1;
    }

    .stat-circle {
        width: 65px;
        height: 65px;
        font-size: var(--text-sm, 0.875rem);
        margin: 0 auto;
    }

    .stat-label {
        font-size: var(--text-sm, 0.875rem);
        line-height: 1.3;
    }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
    .container {
        padding: 0 var(--space-md, 0.75rem);
    }

    .city-hero {
        padding: var(--space-3xl, 2rem) 0;
    }

    .city-title {
        font-size: var(--text-3xl, 1.875rem);
    }

    .city-subtitle {
        font-size: var(--text-lg, 1.125rem);
    }

    .city-badge {
        padding: var(--space-xs, 0.25rem) var(--space-md, 0.75rem);
        font-size: var(--text-sm, 0.875rem);
    }

    .stats-panel {
        padding: var(--space-xl, 1.25rem);
    }

    .stats-list {
        gap: var(--space-sm, 0.5rem);
    }

    .stat-item {
        gap: var(--space-xs, 0.25rem);
    }

    .stat-circle {
        width: 50px;
        height: 50px;
        font-size: var(--text-xs, 0.75rem);
    }

    .stat-label {
        font-size: var(--text-xs, 0.75rem);
        line-height: 1.2;
    }
}
