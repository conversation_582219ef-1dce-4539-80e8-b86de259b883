/* 樱花主题样式覆盖 - 禁用原有城市英雄样式 */

/* 重置原有的城市英雄样式，确保樱花主题正常工作 */
.city-hero::before {
    display: none !important;
}

.city-hero-content {
    position: static !important;
    z-index: auto !important;
}

/* 隐藏原有的城市统计样式 */
.city-stats {
    display: none !important;
}

/* 隐藏原有的城市徽章样式，使用新的樱花主题 */
.city-hero .city-badge {
    background: var(--sakura-100) !important;
    color: var(--sakura-primary) !important;
    backdrop-filter: none !important;
    border: none !important;
}

/* 确保城市动作按钮使用樱花主题 */
.city-actions .btn-secondary {
    display: none !important;
}

/* 移动端优化 - 确保原有样式不干扰 */
@media (max-width: 768px) {
    .city-hero {
        text-align: left !important;
        background: var(--white, #ffffff) !important;
        color: var(--text-primary, #0f172a) !important;
    }
    
    .city-stats {
        display: none !important;
    }
}
