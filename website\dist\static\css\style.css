/* 走马探花静态网站样式 */

/* 基础重置和变量 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调 - 更丰富的紫色系 */
    --primary-color: #6366f1;
    --primary-hover: #4f46e5;
    --primary-light: #a5b4fc;
    --primary-dark: #3730a3;
    --primary-50: #eef2ff;
    --primary-100: #e0e7ff;
    --primary-200: #c7d2fe;
    --primary-300: #a5b4fc;
    --primary-400: #818cf8;
    --primary-500: #6366f1;
    --primary-600: #4f46e5;
    --primary-700: #4338ca;
    --primary-800: #3730a3;
    --primary-900: #312e81;

    /* 辅助色调 */
    --secondary-color: #64748b;
    --secondary-hover: #475569;
    --secondary-light: #94a3b8;
    --secondary-dark: #334155;

    /* 功能色彩 */
    --accent-color: #f59e0b;
    --accent-hover: #d97706;
    --success-color: #10b981;
    --success-hover: #059669;
    --success-600: #047857;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --danger-600: #b91c1c;
    --info-color: #3b82f6;
    --info-hover: #2563eb;

    /* 移动端专用颜色 */
    --mobile-nav-bg: rgba(255, 255, 255, 0.95);
    --mobile-nav-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    --mobile-menu-overlay: rgba(0, 0, 0, 0.5);

    /* RGB值用于透明度计算 */
    --primary-rgb: 99, 102, 241;
    --danger-rgb: 239, 68, 68;
    --success-rgb: 16, 185, 129;

    /* 中性色调 */
    --dark-color: #0f172a;
    --dark-secondary: #1e293b;
    --dark-tertiary: #334155;
    --light-color: #f8fafc;
    --light-secondary: #f1f5f9;
    --light-tertiary: #e2e8f0;
    --white: #ffffff;
    --black: #000000;

    /* 边框颜色 */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --border-dark: #cbd5e1;
    --border-focus: var(--primary-color);

    /* 文字颜色 */
    --text-primary: #0f172a;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --text-light: #cbd5e1;
    --text-white: #ffffff;

    /* 阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    --shadow-glow-lg: 0 0 40px rgba(99, 102, 241, 0.4);

    /* 圆角系统 */
    --border-radius-xs: 4px;
    --border-radius-sm: 6px;
    --border-radius: 8px;
    --border-radius-md: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;
    --border-radius-2xl: 24px;
    --border-radius-3xl: 32px;
    --border-radius-full: 9999px;

    /* 过渡动画 */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 渐变色彩 - 简化版 */
    --gradient-primary: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
    --gradient-secondary: linear-gradient(135deg, #64748b 0%, #475569 100%);
    --gradient-accent: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);

    /* 背景渐变 - 简化版 */
    --bg-gradient-subtle: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);

    /* 间距系统 */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    --space-4xl: 6rem;
    --space-5xl: 8rem;

    /* 字体大小 */
    --text-xs: 0.75rem;
    --text-sm: 0.875rem;
    --text-base: 1rem;
    --text-lg: 1.125rem;
    --text-xl: 1.25rem;
    --text-2xl: 1.5rem;
    --text-3xl: 1.875rem;
    --text-4xl: 2.25rem;
    --text-5xl: 3rem;
    --text-6xl: 3.75rem;

    /* Z-index层级 */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: #ffffff;
    min-height: 100vh;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
    overflow-x: hidden;
}

/* 容器系统 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.container-sm {
    max-width: 640px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.container-md {
    max-width: 768px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.container-lg {
    max-width: 1024px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.container-xl {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.container-fluid {
    width: 100%;
    padding: 0 var(--space-lg);
}

/* 响应式容器 */
@media (max-width: 1280px) {
    .container, .container-xl {
        padding: 0 var(--space-md);
    }
}

@media (max-width: 1024px) {
    .container, .container-xl, .container-lg {
        padding: 0 var(--space-md);
    }
}

@media (max-width: 768px) {
    .container, .container-xl, .container-lg, .container-md {
        padding: 0 var(--space-md);
    }
}

@media (max-width: 640px) {
    .container, .container-xl, .container-lg, .container-md, .container-sm {
        padding: 0 var(--space-sm);
    }
}

/* 通用工具类 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }
.text-5xl { font-size: var(--text-5xl); }
.text-6xl { font-size: var(--text-6xl); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-white { color: var(--text-white); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-white { background-color: var(--white); }
.bg-light { background-color: var(--light-color); }

.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: var(--border-radius-sm); }
.rounded { border-radius: var(--border-radius); }
.rounded-md { border-radius: var(--border-radius-md); }
.rounded-lg { border-radius: var(--border-radius-lg); }
.rounded-xl { border-radius: var(--border-radius-xl); }
.rounded-2xl { border-radius: var(--border-radius-2xl); }
.rounded-3xl { border-radius: var(--border-radius-3xl); }
.rounded-full { border-radius: var(--border-radius-full); }

.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }
.shadow-inner { box-shadow: var(--shadow-inner); }
.shadow-glow { box-shadow: var(--shadow-glow); }

.transition-none { transition: none; }
.transition-fast { transition: var(--transition-fast); }
.transition { transition: var(--transition); }
.transition-slow { transition: var(--transition-slow); }
.transition-bounce { transition: var(--transition-bounce); }

/* 按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border: 1px solid transparent;
    border-radius: var(--border-radius-md);
    font-size: var(--text-sm);
    font-weight: 600;
    line-height: 1.5;
    text-decoration: none;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    user-select: none;
    vertical-align: middle;
    min-height: 44px; /* 触摸友好 */
    box-shadow: var(--shadow-sm);
}

.btn:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 移除复杂的光泽效果 */

/* 主要按钮 - 简化版 */
.btn-primary {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    color: var(--white);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 次要按钮 - 简化版 */
.btn-secondary {
    background: var(--white);
    color: var(--secondary-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--light-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    color: var(--secondary-color);
}

/* 成功按钮 */
.btn-success {
    background: var(--gradient-success);
    color: var(--white);
    border-color: var(--success-color);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

/* 警告按钮 */
.btn-warning {
    background: var(--gradient-warning);
    color: var(--dark-color);
    border-color: var(--warning-color);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--dark-color);
}

/* 危险按钮 */
.btn-danger {
    background: var(--gradient-danger);
    color: var(--white);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

/* 信息按钮 */
.btn-info {
    background: var(--gradient-accent);
    color: var(--white);
    border-color: var(--info-color);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--white);
}

/* 轮廓按钮 - 简化版 */
.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.btn-outline-secondary {
    background: rgba(255, 255, 255, 0.9);
    color: var(--secondary-color);
    border: 2px solid var(--secondary-color);
    backdrop-filter: blur(10px);
}

.btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* 幽灵按钮 */
.btn-ghost {
    background: transparent;
    color: var(--primary-color);
    border: 1px solid transparent;
}

.btn-ghost:hover {
    background: var(--primary-50);
    color: var(--primary-dark);
}

/* 链接按钮 */
.btn-link {
    background: transparent;
    color: var(--primary-color);
    border: none;
    box-shadow: none;
    text-decoration: underline;
    text-underline-offset: 4px;
}

.btn-link:hover {
    color: var(--primary-hover);
    text-decoration: none;
    transform: none;
    box-shadow: none;
}

/* 按钮尺寸 */
.btn-xs {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
    min-height: 32px;
    border-radius: var(--border-radius-sm);
}

.btn-sm {
    padding: var(--space-xs) var(--space-md);
    font-size: var(--text-sm);
    min-height: 36px;
    border-radius: var(--border-radius);
}

.btn-md {
    padding: var(--space-sm) var(--space-lg);
    font-size: var(--text-sm);
    min-height: 44px;
    border-radius: var(--border-radius-md);
}

.btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--text-base);
    min-height: 52px;
    border-radius: var(--border-radius-lg);
}

.btn-xl {
    padding: var(--space-lg) var(--space-2xl);
    font-size: var(--text-lg);
    min-height: 60px;
    border-radius: var(--border-radius-xl);
}

/* 按钮形状 */
.btn-square {
    aspect-ratio: 1;
    padding: var(--space-sm);
}

.btn-circle {
    aspect-ratio: 1;
    padding: var(--space-sm);
    border-radius: var(--border-radius-full);
}

.btn-wide {
    padding-left: var(--space-2xl);
    padding-right: var(--space-2xl);
}

.btn-block {
    width: 100%;
    justify-content: center;
}

/* 按钮组 */
.btn-group {
    display: inline-flex;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    border-radius: 0;
    border-right-width: 0;
    box-shadow: none;
}

.btn-group .btn:first-child {
    border-top-left-radius: var(--border-radius-md);
    border-bottom-left-radius: var(--border-radius-md);
}

.btn-group .btn:last-child {
    border-top-right-radius: var(--border-radius-md);
    border-bottom-right-radius: var(--border-radius-md);
    border-right-width: 1px;
}

.btn-group .btn:hover {
    z-index: 1;
    border-right-width: 1px;
}

/* 加载状态 */
.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid currentColor;
    border-radius: 50%;
    border-top-color: transparent;
    animation: btn-spin 0.8s linear infinite;
}

@keyframes btn-spin {
    to {
        transform: rotate(360deg);
    }
}

/* 头部导航系统 - 优化版 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 搜索框样式 */
.nav-search {
    display: flex;
    align-items: center;
    margin: 0 1rem;
}

.search-form-header {
    display: flex;
    align-items: center;
    background: var(--light-color);
    border-radius: var(--border-radius-full);
    padding: 0.25rem;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    width: 280px;
}

.search-form-header:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-input-header {
    border: none;
    background: transparent;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    color: var(--text-primary);
    flex: 1;
    outline: none;
}

.search-input-header::placeholder {
    color: var(--text-muted);
}

.search-btn-header {
    background: var(--primary-color);
    border: none;
    border-radius: var(--border-radius-full);
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn-header:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.search-icon {
    font-size: 1rem;
    color: white;
}

/* 突出的CTA按钮 */
.nav-cta-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius-full);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    transition: var(--transition);
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    margin-left: 1rem;
}

.nav-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
    color: white;
}

.cta-icon {
    font-size: 1.1rem;
}

/* 移动端导航按钮 */
.mobile-nav-actions {
    display: none;
    align-items: center;
    gap: 0.75rem;
}

.mobile-search-btn,
.mobile-cta-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: var(--border-radius-md);
    text-decoration: none;
    transition: var(--transition);
}

.mobile-search-btn {
    background: var(--light-color);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.mobile-search-btn:hover {
    background: var(--primary-50);
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.mobile-cta-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    color: white;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

.mobile-cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.4);
    color: white;
}

.navbar {
    padding: var(--space-lg) 0;
    transition: var(--transition);
}

.header.scrolled .navbar {
    padding: var(--space-md) 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

/* 品牌标识 */
.navbar-brand {
    z-index: var(--z-dropdown);
}

.navbar-brand .brand-link {
    text-decoration: none;
    color: var(--text-primary);
    transition: var(--transition);
}

.navbar-brand .brand-link:hover {
    transform: scale(1.02);
}

.brand-logo {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.brand-icon {
    font-size: var(--text-4xl);
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    transition: var(--transition);
}

.brand-icon:hover {
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.15));
    transform: rotate(5deg);
}

.brand-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.brand-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2px;
    color: var(--primary-color);
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-weight: 400;
}

.brand-link:hover .brand-subtitle {
    opacity: 1;
    color: var(--text-secondary);
}

/* 导航菜单 */
.navbar-menu {
    display: flex;
    align-items: center;
}

.navbar-nav {
    display: flex;
    align-items: center;
    gap: var(--space-2xl);
    list-style: none;
    margin: 0;
    padding: 0;
}

/* 导航链接 */
.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 600;
    font-size: var(--text-sm);
    padding: var(--space-sm) 0;
    position: relative;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
    border-radius: var(--border-radius-full);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link:hover {
    color: var(--primary-color);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    width: 100%;
}

/* 下拉菜单 */
.nav-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.dropdown-toggle::before {
    content: '▼';
    font-size: var(--text-xs);
    transition: var(--transition);
    opacity: 0.7;
}

.nav-dropdown:hover .dropdown-toggle::before {
    transform: rotate(180deg);
    opacity: 1;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--white);
    border: 1px solid var(--border-light);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--space-sm);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--transition);
    z-index: var(--z-dropdown);
    backdrop-filter: blur(20px);
}

.nav-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}



.dropdown-link {
    display: block;
    padding: var(--space-xs) var(--space-sm);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: var(--text-sm);
    margin-bottom: var(--space-xs);
}

.dropdown-link:hover {
    background: var(--primary-50);
    color: var(--primary-color);
    transform: translateX(4px);
}

.dropdown-link:last-child {
    margin-bottom: 0;
}

/* Telegram链接特殊样式 */
.telegram-link {
    background: var(--gradient-primary);
    color: var(--white) !important;
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.telegram-link::after {
    display: none;
}

.telegram-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.telegram-link:hover::before {
    left: 100%;
}

.telegram-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--white) !important;
}

.telegram-link:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* 移动端菜单系统 */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    cursor: pointer;
    gap: 4px;
    border: none;
    background: transparent;
    border-radius: var(--border-radius);
    transition: var(--transition);
    z-index: var(--z-dropdown);
}

.mobile-menu-toggle:hover {
    background: var(--primary-50);
}

.mobile-menu-toggle span {
    width: 24px;
    height: 3px;
    background-color: var(--text-primary);
    transition: var(--transition);
    border-radius: var(--border-radius-full);
    transform-origin: center;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
    background-color: var(--primary-color);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
    background-color: var(--primary-color);
}

/* 移动端菜单覆盖层 */
.mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    z-index: var(--z-modal-backdrop);
}

.mobile-menu-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* 移动端菜单面板 */
.mobile-menu-panel {
    position: fixed;
    top: 0;
    right: -100%;
    width: 320px;
    max-width: 90vw;
    height: 100vh;
    background: var(--white);
    box-shadow: var(--shadow-2xl);
    transition: var(--transition);
    z-index: var(--z-modal);
    overflow-y: auto;
}

.mobile-menu-panel.active {
    right: 0;
}

.mobile-menu-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.mobile-menu-close {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--light-color);
    border-radius: var(--border-radius-full);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.mobile-menu-close:hover {
    background: var(--primary-color);
    color: var(--white);
}

.mobile-menu-content {
    padding: var(--space-lg);
}

.mobile-nav-section {
    margin-bottom: var(--space-xl);
}

.mobile-nav-title {
    font-size: var(--text-sm);
    font-weight: 700;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--space-md);
}

.mobile-nav-links {
    list-style: none;
    margin: 0;
    padding: 0;
}

.mobile-nav-link {
    display: block;
    padding: var(--space-md);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    margin-bottom: var(--space-xs);
    font-weight: 500;
}

.mobile-nav-link:hover {
    background: var(--primary-50);
    color: var(--primary-color);
    transform: translateX(4px);
}

.mobile-nav-link.active {
    background: var(--primary-color);
    color: var(--white);
}

/* 移动端下拉菜单 */
.mobile-dropdown {
    margin-bottom: var(--space-sm);
}

.mobile-dropdown-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--space-md);
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-weight: 500;
    border-radius: var(--border-radius-lg);
    transition: var(--transition);
    cursor: pointer;
}

.mobile-dropdown-toggle:hover {
    background: var(--light-color);
}

.mobile-dropdown-icon {
    transition: var(--transition);
}

.mobile-dropdown.active .mobile-dropdown-icon {
    transform: rotate(180deg);
}

.mobile-dropdown-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    padding-left: var(--space-lg);
}

.mobile-dropdown.active .mobile-dropdown-content {
    max-height: 500px;
}

.mobile-dropdown-link {
    display: block;
    padding: var(--space-sm) var(--space-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: var(--text-sm);
    margin-bottom: var(--space-xs);
}

.mobile-dropdown-link:hover {
    background: var(--primary-50);
    color: var(--primary-color);
}

/* 面包屑导航 */
.breadcrumb {
    background-color: var(--light-color);
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.breadcrumb-list {
    display: flex;
    align-items: center;
    list-style: none;
    gap: 0.5rem;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: ">";
    margin-left: 0.5rem;
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: var(--text-secondary);
}

/* 主要内容区域 */
.main {
    min-height: calc(100vh - 200px);
}

/* 英雄区域 - 简化版 */
.hero {
    background: #ffffff !important;
    color: #000000 !important;
    padding: 4rem 0;
    text-align: center;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    color: #000000;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-weight: 500;
    color: #000000;
}

.hero-description {
    font-size: 1rem;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.5;
    color: #000000;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

/* Hero按钮特殊样式 - 仿Notion设计 */
.btn-hero-primary,
.btn-hero-secondary {
    display: inline-block;
    width: 140px;
    height: 48px;
    line-height: 46px;
    text-align: center;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
}

.btn-hero-primary {
    background: #0066cc !important;
    color: #ffffff !important;
    border: 1px solid #0066cc !important;
    box-shadow: 0 2px 8px rgba(0, 102, 204, 0.2);
}

.btn-hero-secondary {
    background: #ffffff !important;
    color: #333333 !important;
    border: 1px solid #e0e0e0 !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-hero-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.btn-hero-primary:hover::before {
    left: 100%;
}

.btn-hero-primary:hover {
    background: #0052a3 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 102, 204, 0.3);
    color: #ffffff !important;
}

.btn-hero-secondary:hover {
    background: #f8f9fa !important;
    border-color: #d0d0d0 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    color: #333333 !important;
}

.btn-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.btn-text {
    margin-right: 0.5rem;
}

.btn-arrow {
    font-size: 1rem;
    transition: var(--transition);
}

.btn-hero-primary:hover .btn-arrow {
    transform: translateX(4px);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

/* 强制移动端水平布局 - 添加更高优先级 */
@media screen and (max-width: 767px) {
    .hero-stats {
        display: flex !important;
        flex-direction: row !important;
        justify-content: space-between !important;
        align-items: stretch !important;
        gap: 0.5rem !important;
        margin-top: 2rem !important;
        flex-wrap: nowrap !important;
        overflow-x: visible !important;
        padding: 0 1rem !important;
        width: 100% !important;
        box-sizing: border-box !important;
    }

    .stat-item {
        flex: 1 1 33.333% !important;
        min-width: 0 !important;
        max-width: none !important;
        padding: 1rem 0.5rem !important;
        margin: 0 !important;
        text-align: center !important;
        background: #f8f9fa !important;
        border: 1px solid #e9ecef !important;
        border-radius: 12px !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
    }

    .stat-number {
        font-size: 1.8rem !important;
        line-height: 1.2 !important;
        margin-bottom: 0.25rem !important;
        color: #0066cc !important;
        font-weight: 800 !important;
        display: block !important;
    }

    .stat-label {
        font-size: 0.7rem !important;
        line-height: 1.2 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        color: #666666 !important;
        font-weight: 600 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }
}

.stat-item {
    text-align: center;
    padding: 1.5rem 2rem;
    background: #f8f9fa;
    border-radius: var(--border-radius-xl);
    min-width: 150px;
    border: 1px solid #e9ecef;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 102, 204, 0.1), transparent);
    transition: left 0.6s ease;
}

.stat-item:hover::before {
    left: 100%;
}

.stat-item:hover {
    transform: translateY(-4px);
    background: #ffffff;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
    border-color: #0066cc;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: #0066cc;
    text-shadow: none;
}

.stat-label {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #666666;
}

/* 移动端优化 - 确保数据展示模块水平排列 */
@media (max-width: 768px) {
    .hero {
        padding: 3rem 0 !important;
    }

    .hero-actions {
        gap: 1rem !important;
        flex-direction: column !important;
        align-items: center !important;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        width: 200px !important;
        margin: 0 !important;
    }
}

/* 城市首页样式 */
.city-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-600) 100%);
    color: white;
    padding: var(--space-4xl) 0 var(--space-3xl);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.city-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.city-hero-content {
    position: relative;
    z-index: 1;
}

.city-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-sm);
    background: rgba(255, 255, 255, 0.2);
    padding: var(--space-sm) var(--space-lg);
    border-radius: var(--border-radius-full);
    margin-bottom: var(--space-lg);
    backdrop-filter: blur(10px);
}

.city-icon {
    font-size: 1.2rem;
}

.city-code {
    font-weight: 700;
    font-size: var(--text-sm);
    letter-spacing: 0.1em;
}

.city-title {
    font-size: var(--text-5xl);
    font-weight: 800;
    margin-bottom: var(--space-sm);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.city-subtitle {
    font-size: var(--text-xl);
    font-weight: 600;
    margin-bottom: var(--space-lg);
    opacity: 0.9;
}

.city-description {
    font-size: var(--text-lg);
    line-height: 1.6;
    margin-bottom: var(--space-2xl);
    opacity: 0.9;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.city-stats {
    display: flex;
    justify-content: center;
    gap: var(--space-2xl);
    margin-bottom: var(--space-2xl);
}

.city-stats .stat-item {
    text-align: center;
}

.city-stats .stat-number {
    font-size: var(--text-3xl);
    font-weight: 800;
    display: block;
    margin-bottom: var(--space-xs);
}

.city-stats .stat-label {
    font-size: var(--text-sm);
    opacity: 0.8;
    font-weight: 600;
}

.city-actions {
    display: flex;
    justify-content: center;
    gap: var(--space-lg);
    flex-wrap: wrap;
}

.city-services-section {
    padding: var(--space-4xl) 0;
    background: var(--light-color);
}

.other-cities-section {
    padding: var(--space-4xl) 0;
    background: white;
}

.city-name-link {
    color: inherit;
    text-decoration: none;
    transition: var(--transition);
}

.city-name-link:hover {
    color: var(--primary-color);
}

.city-footer {
    margin-top: var(--space-lg);
    text-align: center;
}

.city-overview-link {
    font-weight: 600;
    color: var(--primary-color) !important;
    border-left: 3px solid var(--primary-color) !important;
}

/* 页面头部 */
.page-header {
    background-color: var(--light-color);
    padding: 3rem 0;
    text-align: center;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.page-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.header-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* 分区样式 */
.section {
    padding: 5rem 0;
    position: relative;
}

.testimonials-section,
.value-section,
.how-it-works,
.cities-section,
.services-section,
.featured-section {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(20px);
    margin: 2rem 0;
    border-radius: var(--border-radius-xl);
    border: 1px solid var(--border-light);
}

/* 平台优势区域特殊样式 */
.value-section {
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(255, 255, 255, 0.8) 100%);
    border: 1px solid rgba(99, 102, 241, 0.1);
    position: relative;
    overflow: hidden;
}

.value-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="value-dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="0.8" fill="rgba(99,102,241,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23value-dots)"/></svg>');
    opacity: 0.6;
}

.value-section .container {
    position: relative;
    z-index: 2;
}

.value-section .cta-features {
    margin-bottom: 0;
    margin-top: 2rem;
}

/* 用户评价区域特殊样式 */
.testimonials-section {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 1px solid rgba(16, 185, 129, 0.1);
    position: relative;
    overflow: hidden;
}

.testimonials-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="testimonial-dots" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="12.5" cy="12.5" r="1" fill="rgba(16,185,129,0.06)"/></pattern></defs><rect width="100" height="100" fill="url(%23testimonial-dots)"/></svg>');
    opacity: 0.7;
}

.testimonials-section .container {
    position: relative;
    z-index: 2;
}

.testimonials-section .testimonials {
    margin-bottom: 0;
    margin-top: 2rem;
}

/* 使用流程区域特殊样式 */
.how-it-works {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.how-it-works::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="steps-dots" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="1.2" fill="rgba(59,130,246,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23steps-dots)"/></svg>');
    opacity: 0.6;
}

.how-it-works .container {
    position: relative;
    z-index: 2;
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.step-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(59, 130, 246, 0.15);
    border-radius: var(--border-radius-xl);
    padding: 2rem;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.1);
}

.step-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(59, 130, 246, 0.15);
    border-color: rgba(59, 130, 246, 0.25);
}

.step-number {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, var(--info-color), var(--info-hover));
    color: white;
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.875rem;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.step-icon {
    font-size: 3rem;
    margin: 1rem 0;
    display: block;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.step-description {
    font-size: 1rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* 移动端响应式 */
@media (max-width: 768px) {
    .steps-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .step-item {
        padding: 1.5rem;
    }

    .step-icon {
        font-size: 2.5rem;
        margin: 0.75rem 0;
    }

    .step-title {
        font-size: 1.125rem;
    }

    .step-description {
        font-size: 0.9rem;
    }
}

.section-title {
    font-size: 2.5rem;
    font-weight: 800;
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.25rem;
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.section-footer {
    text-align: center;
    margin-top: 4rem;
}

/* 网格布局 */
.cities-grid,
.services-grid,
.shops-grid,
.categories-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.cities-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.services-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.shops-grid {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

/* 卡片系统 - 简化版 */
.card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    position: relative;
}

/* 卡片悬停效果 - 简化版 */
.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: var(--primary-200);
}

/* 卡片内容区域 */
.card-header {
    margin-bottom: var(--space-lg);
}

.card-body {
    margin-bottom: var(--space-lg);
}

.card-footer {
    margin-top: auto;
    padding-top: var(--space-lg);
    border-top: 1px solid var(--border-light);
}

/* 卡片变体 */
.card-elevated {
    box-shadow: var(--shadow-lg);
}

.card-elevated:hover {
    box-shadow: var(--shadow-2xl);
    transform: translateY(-12px);
}

.card-flat {
    box-shadow: none;
    border: 2px solid var(--border-color);
}

.card-flat:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
}

.card-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-gradient {
    background: var(--gradient-light);
    border: none;
}

/* 特定卡片类型 - 简化版 */
.city-card,
.service-card,
.shop-card,
.category-card {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
    position: relative;
}

.city-card:hover,
.service-card:hover,
.shop-card:hover,
.category-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: var(--primary-200);
}

/* 城市卡片特殊效果 */
.city-card::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, var(--primary-100) 0%, transparent 70%);
    opacity: 0;
    transition: var(--transition);
    pointer-events: none;
}

.city-card:hover::after {
    opacity: 0.3;
    top: -25%;
    right: -25%;
}

/* 城市特色图标 */
.city-icon-wrapper {
    position: absolute;
    top: -15px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    z-index: 2;
}

.city-feature-icon {
    font-size: 1.8rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

/* 城市统计数据 */
.city-stats-mini {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-light);
}

.stat-mini {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 50px;
}

.stat-mini-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.stat-mini-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-weight: 500;
}

.city-hot-badge {
    margin-left: auto;
}

.hot-indicator,
.active-indicator {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-full);
    font-weight: 600;
}

.hot-indicator {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.active-indicator {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

/* 服务预览链接 */
.city-services-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin: 1rem 0;
}

.service-preview-link {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.375rem 0.75rem;
    background: var(--primary-50);
    color: var(--primary-color);
    text-decoration: none;
    border-radius: var(--border-radius-full);
    font-size: 0.8rem;
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid var(--primary-200);
}

.service-preview-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.more-services {
    font-size: 0.75rem;
    color: var(--text-muted);
    padding: 0.375rem 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius-full);
    border: 1px solid var(--border-light);
}

/* 城市按钮样式 */
.btn-city:hover .btn-arrow {
    transform: translateX(4px);
}

/* 服务卡片特殊效果 */
.service-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    background: var(--gradient-accent);
}

/* 服务图标包装器 */
.service-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
    position: relative;
    overflow: hidden;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.service-card:hover .service-icon::before {
    left: 100%;
}

.service-badge {
    position: absolute;
    top: -8px;
    right: -8px;
}

.badge-hot,
.badge-popular,
.badge-new {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-full);
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-hot {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
}

.badge-popular {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.badge-new {
    background: linear-gradient(135deg, #feca57, #ff9ff3);
    color: white;
}

/* 服务统计 */
.service-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--border-light);
}

.stat-item-service {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.service-count {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
}

.service-count-label {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-weight: 500;
}

.service-rating {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.rating-stars {
    font-size: 0.8rem;
    line-height: 1;
    margin-bottom: 0.25rem;
}

.rating-text {
    font-size: 0.7rem;
    color: var(--text-muted);
    font-weight: 500;
}

/* 服务预览 */
.service-preview {
    margin: 1rem 0;
    padding: 0.75rem;
    background: var(--primary-50);
    border-radius: var(--border-radius-md);
    border: 1px solid var(--primary-200);
}

.preview-shops {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: center;
}

.preview-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 600;
}

.preview-shop {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    background: white;
    color: var(--primary-color);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

.preview-more {
    font-size: 0.75rem;
    color: var(--text-muted);
    font-style: italic;
}

/* 服务链接按钮 */
.service-link-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    justify-content: center;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: var(--transition);
    margin-top: auto;
}

.service-link-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    color: white;
}

.service-link-btn:hover .btn-arrow {
    transform: translateX(4px);
}

/* 商家卡片特殊效果 */
.shop-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.98) 0%, rgba(251,252,254,0.98) 100%);
    position: relative;
    overflow: hidden;
}

.shop-card::before {
    background: var(--gradient-secondary);
}

/* 商家图片包装器 */
.shop-image-wrapper {
    position: relative;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
}

.shop-avatar {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    box-shadow: 0 6px 20px rgba(99, 102, 241, 0.3);
    flex-shrink: 0;
}

.shop-badges {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    align-items: flex-end;
}

.badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-full);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    white-space: nowrap;
}

.badge-featured {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #8b5a00;
}

.badge-quality {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
    color: white;
}

.badge-verified {
    background: linear-gradient(135deg, #6366f1, #4f46e5);
    color: white;
}

/* 分类标签样式 */
.category-tag {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-full);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.category-下水 {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

.category-按摩 {
    background: linear-gradient(135deg, #10b981, #047857);
    color: white;
}

.category-b2b {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.category-tag:not([class*="category-"]) {
    background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
    color: white;
}

/* 商家描述 */
.shop-description-wrapper {
    margin-bottom: 1rem;
}

.shop-description {
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 0.5rem;
}

.description-toggle {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.8rem;
    cursor: pointer;
    text-decoration: underline;
    padding: 0;
}

/* 评分显示 */
.rating-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rating-stars {
    display: flex;
    gap: 1px;
}

.star {
    font-size: 0.9rem;
    opacity: 0.3;
}

.star.filled {
    opacity: 1;
}

.rating-text {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.rating-count {
    color: var(--text-muted);
    font-size: 0.8rem;
}

.no-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.rating-placeholder {
    opacity: 0.3;
}

/* 联系信息 */
.contact-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius-md);
    margin-bottom: 1rem;
    border: 1px solid var(--border-light);
}

.contact-icon {
    font-size: 1.1rem;
}

.contact-text {
    font-weight: 500;
    color: var(--text-primary);
}

.contact-hint {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-left: auto;
}

/* 商家操作按钮 */
.shop-actions {
    display: flex;
    gap: 0.75rem;
    margin-top: auto;
}

.btn-shop-detail,
.btn-shop-contact {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
}

.btn-shop-contact {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    position: relative;
    overflow: hidden;
}

.btn-shop-contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.btn-shop-contact:hover::before {
    left: 100%;
}

/* 分类卡片特殊效果 */
.category-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.96) 0%, rgba(249,250,251,0.96) 100%);
}

.category-card::before {
    background: var(--gradient-success);
}

/* 卡片网格布局增强 */
.card-grid {
    display: grid;
    gap: var(--space-xl);
    margin-bottom: var(--space-2xl);
}

.card-grid-1 { grid-template-columns: 1fr; }
.card-grid-2 { grid-template-columns: repeat(2, 1fr); }
.card-grid-3 { grid-template-columns: repeat(3, 1fr); }
.card-grid-4 { grid-template-columns: repeat(4, 1fr); }

.card-grid-auto {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.card-grid-auto-sm {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.card-grid-auto-lg {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

/* 响应式卡片网格 */
@media (max-width: 1024px) {
    .card-grid-4 { grid-template-columns: repeat(3, 1fr); }
    .card-grid-3 { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
    .card-grid-4,
    .card-grid-3,
    .card-grid-2 {
        grid-template-columns: 1fr;
    }

    .card {
        padding: var(--space-lg);
    }

    .city-card,
    .service-card,
    .shop-card,
    .category-card {
        padding: var(--space-lg);
    }
}

/* 卡片加载状态 */
.card-loading {
    position: relative;
    overflow: hidden;
}

.card-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: card-shimmer 1.5s infinite;
}

@keyframes card-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 城市卡片 */
.city-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
}

.city-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.city-code {
    background: var(--gradient-accent);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
    font-weight: 600;
    box-shadow: var(--shadow-sm);
}

.city-description {
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 1rem;
    line-height: 1.6;
}

.city-services {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.service-link {
    background: linear-gradient(135deg, var(--light-secondary) 0%, rgba(255,255,255,0.8) 100%);
    color: var(--text-primary);
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    border: 1px solid var(--border-light);
    backdrop-filter: blur(10px);
}

.service-link:hover {
    background: var(--gradient-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* 商家卡片 */
.shop-header {
    margin-bottom: 1rem;
}

.shop-name {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.shop-badges {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 500;
}

.badge-category {
    background-color: var(--primary-color);
    color: white;
}

.badge-type {
    background-color: var(--secondary-color);
    color: white;
}

.badge-location {
    background-color: var(--success-color);
    color: white;
}

.shop-description {
    margin-bottom: 1rem;
}

.shop-description p {
    color: var(--text-secondary);
    line-height: 1.5;
}

.shop-details {
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.detail-icon {
    width: 16px;
    text-align: center;
}

/* 评分样式 */
.shop-rating,
.rating-display {
    margin-bottom: 1rem;
}

.rating-stars {
    display: inline-flex;
    gap: 2px;
}

.star {
    font-size: 0.875rem;
    color: #d1d5db;
}

.star.filled {
    color: #fbbf24;
}

.rating-text {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.rating-count {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.no-rating {
    color: var(--text-muted);
    font-size: 0.875rem;
}

/* 联系方式隐藏 */
.contact-hidden {
    background-color: var(--light-color);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
}

.contact-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.contact-hints {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.hint-badge {
    background-color: white;
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    border: 1px solid var(--border-color);
}

/* 操作按钮 */
.shop-actions {
    display: flex;
    gap: 0.75rem;
}

.shop-actions .btn {
    flex: 1;
    font-size: 0.875rem;
    padding: 0.625rem 1rem;
}

/* CTA区域 - 重新设计 */
.cta-section {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #020617 100%);
    color: white;
    padding: 5rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(99,102,241,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.5;
}

.cta-content {
    position: relative;
    z-index: 2;
}

.cta-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-full);
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.badge-icon {
    font-size: 1rem;
}

.cta-title {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.cta-description {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    opacity: 0.9;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* 用户证言 */
.testimonials {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.testimonial-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: var(--border-radius-xl);
    max-width: 300px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 独立 testimonials-section 中的样式优化 */
.testimonials-section .testimonial-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(16, 185, 129, 0.15);
    box-shadow: 0 4px 20px rgba(16, 185, 129, 0.1);
    transition: all 0.3s ease;
}

.testimonials-section .testimonial-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(16, 185, 129, 0.15);
    border-color: rgba(16, 185, 129, 0.25);
}

.testimonials-section .testimonial-avatar {
    background: linear-gradient(135deg, var(--success-color), var(--success-600));
}

.testimonial-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-600));
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.testimonial-content p {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-style: italic;
    line-height: 1.4;
}

.testimonial-author {
    font-size: 0.8rem;
    opacity: 0.8;
    font-weight: 500;
}

.cta-actions {
    margin-bottom: 3rem;
}

/* CTA主按钮 */
.btn-cta-main {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #ffffff, #f0f9ff);
    color: var(--primary-color);
    padding: 1.5rem 3rem;
    border-radius: var(--border-radius-xl);
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn-cta-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: left 0.6s ease;
}

.btn-cta-main:hover::before {
    left: 100%;
}

.btn-cta-main:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 50px rgba(0, 0, 0, 0.3);
    color: var(--primary-color);
}

.btn-subtext {
    font-size: 0.8rem;
    font-weight: 500;
    opacity: 0.8;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: 3rem;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    max-width: 150px;
    background: #ffffff;
    border-radius: 12px;
    padding: 1.25rem;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 6px 16px rgba(0,0,0,0.08);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 0.75rem;
}

.feature-text {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
    display: block;
}

.feature-desc {
    font-size: 0.8rem;
    opacity: 0.8;
    text-align: center;
    line-height: 1.4;
    color: #4b5563;
}

/* 信任指标 */
.trust-indicators {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-full);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.trust-icon {
    font-size: 1.1rem;
}

.trust-text {
    font-size: 0.9rem;
    font-weight: 500;
}

/* FAQ区域 */
.faq-section {
    background: var(--light-color);
    padding: 5rem 0;
}

.faq-header {
    text-align: center;
    margin-bottom: 4rem;
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.faq-item {
    background: white;
    border-radius: var(--border-radius-xl);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: var(--transition);
}

.faq-item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.faq-question {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
    background: white;
}

.faq-question:hover {
    background: var(--primary-50);
}

.faq-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.faq-question h3 {
    flex: 1;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.faq-toggle {
    font-size: 1.5rem;
    font-weight: 300;
    color: var(--primary-color);
    transition: var(--transition);
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-50);
    border-radius: var(--border-radius-full);
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
    background: var(--primary-color);
    color: white;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: var(--light-color);
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-answer p {
    padding: 1.5rem;
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.6;
}

.faq-footer {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius-xl);
    border: 1px solid var(--border-light);
}

.faq-footer-text {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* 搜索页面样式 */
.search-header {
    background-color: var(--light-color);
    padding: 3rem 0;
    text-align: center;
}

.search-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.search-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.search-form {
    max-width: 600px;
    margin: 0 auto;
}

.search-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background-color: var(--primary-hover);
}

.search-filters {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 1rem;
}

.filter-select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 0.875rem;
    transition: var(--transition);
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-btn {
    padding: 0.5rem 0.75rem;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    cursor: pointer;
    transition: var(--transition);
}

.clear-btn:hover {
    background-color: #4b5563;
    transform: translateY(-1px);
}

/* 搜索结果样式增强 */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.results-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.results-count {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.results-sort select {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 0.875rem;
}

/* 底部 - 现代化设计 */
.footer {
    position: relative;
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 50%, #020617 100%);
    color: white;
    padding: 4rem 0 1rem;
    margin-top: 4rem;
    overflow: hidden;
}

/* 装饰性背景元素 */
.footer-bg-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
}

.footer-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(99, 102, 241, 0.1);
    animation: float 6s ease-in-out infinite;
}

.footer-circle-1 {
    width: 200px;
    height: 200px;
    top: -100px;
    right: -100px;
    animation-delay: 0s;
}

.footer-circle-2 {
    width: 150px;
    height: 150px;
    bottom: -75px;
    left: -75px;
    animation-delay: 3s;
}

.footer-wave {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100px;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.05), transparent);
    transform: skewY(-1deg);
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* 页脚内容区域 */
.footer .container {
    position: relative;
    z-index: 2;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 3rem;
}

/* 品牌区域 */
.footer-brand {
    max-width: 350px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.footer-logo-icon {
    font-size: 2rem;
    filter: drop-shadow(0 0 10px rgba(99, 102, 241, 0.5));
}

.footer-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    background: linear-gradient(135deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-description {
    color: #cbd5e1;
    line-height: 1.7;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

/* 社交链接 */
.footer-social {
    margin-bottom: 2rem;
}

.social-link.telegram-cta {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.875rem 1.5rem;
    background: linear-gradient(135deg, #229ED9, #0088CC);
    border-radius: 12px;
    text-decoration: none;
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(34, 158, 217, 0.3);
    position: relative;
    overflow: hidden;
}

.social-link.telegram-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.social-link.telegram-cta:hover::before {
    left: 100%;
}

.social-link.telegram-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(34, 158, 217, 0.4);
    background: linear-gradient(135deg, #0088CC, #229ED9);
}

.telegram-logo {
    width: 20px;
    height: 20px;
    color: white;
    flex-shrink: 0;
}

.social-text {
    font-size: 0.95rem;
}

.social-arrow {
    font-size: 1.1rem;
    transition: transform 0.3s ease;
}

.social-link.telegram-cta:hover .social-arrow {
    transform: translateX(3px);
}

/* 统计信息 */
.footer-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #6366f1;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #94a3b8;
    margin-top: 0.25rem;
}

/* 页脚子标题 */
.footer-subtitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #f1f5f9;
}

.subtitle-icon {
    font-size: 1.2rem;
}

/* 页脚链接 */
.footer-links {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.25rem 0;
    border-radius: 6px;
    font-size: 0.9rem;
}

.footer-link:hover {
    color: white;
    transform: translateX(5px);
    background: rgba(99, 102, 241, 0.1);
    padding-left: 0.5rem;
}

.link-icon {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* 底部版权区域 */
.footer-bottom {
    border-top: 1px solid rgba(148, 163, 184, 0.2);
    padding-top: 2rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-copyright {
    color: #94a3b8;
    font-size: 0.875rem;
}

.footer-links-bottom {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.bottom-link {
    color: #cbd5e1;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.bottom-link:hover {
    color: white;
}

.link-separator {
    color: #64748b;
    font-size: 0.875rem;
}

/* 动画系统 */

/* 基础动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* 特效动画 */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translateY(0);
    }
    40%, 43% {
        transform: translateY(-15px);
    }
    70% {
        transform: translateY(-7px);
    }
    90% {
        transform: translateY(-3px);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes swing {
    20% {
        transform: rotate(15deg);
    }
    40% {
        transform: rotate(-10deg);
    }
    60% {
        transform: rotate(5deg);
    }
    80% {
        transform: rotate(-5deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

/* 波纹效果 */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* 闪烁效果 */
@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* 渐变动画 */
@keyframes gradientShift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 动画类 */
.animate-fadeIn {
    animation: fadeIn 0.6s ease-out;
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInDown {
    animation: fadeInDown 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-slideInUp {
    animation: slideInUp 0.6s ease-out;
}

.animate-slideInDown {
    animation: slideInDown 0.6s ease-out;
}

.animate-zoomIn {
    animation: zoomIn 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-rotate {
    animation: rotate 1s linear infinite;
}

.animate-swing {
    animation: swing 1s ease-in-out;
}

/* 延迟动画 */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }
.animate-delay-900 { animation-delay: 0.9s; }
.animate-delay-1000 { animation-delay: 1s; }

/* 动画持续时间 */
.animate-fast { animation-duration: 0.3s; }
.animate-slow { animation-duration: 1s; }
.animate-slower { animation-duration: 2s; }

/* 动画填充模式 */
.animate-fill-both { animation-fill-mode: both; }
.animate-fill-forwards { animation-fill-mode: forwards; }
.animate-fill-backwards { animation-fill-mode: backwards; }

/* 简化的动画效果 */
.hero-content {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
}

/* 移除过多的延迟动画 */

.stat-item:hover {
    animation: float 2s ease-in-out infinite;
}

/* 加载动画 */
.loading {
    opacity: 0;
    animation: fadeInUp 0.6s ease-out forwards;
}

/* 渐变文字效果 */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 玻璃态效果 */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 阴影效果 */
.shadow-glow {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

.shadow-glow:hover {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
}
