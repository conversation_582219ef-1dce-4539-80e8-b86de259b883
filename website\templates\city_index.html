{% extends "base.html" %}

{% block title %}{{ page_title }}{% endblock %}
{% block description %}{{ page_description }}{% endblock %}
{% block keywords %}{{ page_keywords }}{% endblock %}

{% block canonical_url %}/{{ city_code }}/{% endblock %}
{% block canonical %}/{{ city_code }}/{% endblock %}

{% block breadcrumb %}
{% set breadcrumb_items = [
    {"name": city_info.name, "url": None}
] %}
{{ super() }}
{% endblock %}

{% block structured_data %}
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "{{ config.SITE_NAME }} - {{ city_info.name }}",
    "@id": "{{ config.SITE_URL }}/{{ city_code }}/",
    "url": "{{ config.SITE_URL }}/{{ city_code }}/",
    "description": "{{ page_description }}",
    "address": {
        "@type": "PostalAddress",
        "addressLocality": "{{ city_info.name }}",
        "addressCountry": "MY"
    },
    "areaServed": {
        "@type": "City",
        "name": "{{ city_info.name }}"
    },
    "serviceArea": {
        "@type": "GeoCircle",
        "geoMidpoint": {
            "@type": "GeoCoordinates",
            "latitude": {{ city_info.get('latitude', 3.1390) }},
            "longitude": {{ city_info.get('longitude', 101.6869) }}
        },
        "geoRadius": "50000"
    },
    "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "{{ city_info.name }}服务目录",
        "itemListElement": [
            {% for service_name, service_info in config.SERVICES.items() %}
            {
                "@type": "Offer",
                "itemOffered": {
                    "@type": "Service",
                    "name": "{{ city_info.name }}{{ service_name }}",
                    "description": "{{ city_info.name }}地区专业{{ service_name }}"
                }
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ]
    }
}
</script>
{% endblock %}

{% block content %}
<!-- City Hero Section -->
<section class="city-hero">
    <div class="container">
        <div class="city-hero-content">
            <div class="city-badge">
                <span class="city-icon">📍</span>
                <span class="city-code">{{ city_info.code }}</span>
            </div>
            <h1 class="city-title">{{ city_info.name }}</h1>
            <h2 class="city-subtitle">{{ city_info.description }}</h2>
            <p class="city-description">
                走马探花为您提供{{ city_info.name }}地区最全面的优质服务信息<br>
                通过Telegram机器人获取详细联系方式和服务详情
            </p>
            
            <div class="city-stats">
                <div class="stat-item">
                    <div class="stat-number">{{ total_shops }}</div>
                    <div class="stat-label">优质商家</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ config.SERVICES|length }}</div>
                    <div class="stat-label">服务分类</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">24/7</div>
                    <div class="stat-label">在线服务</div>
                </div>
            </div>
            
            <div class="city-actions">
                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}" class="btn btn-primary" target="_blank">
                    📱 联系机器人
                </a>
                <a href="/search.html?city={{ city_code }}" class="btn btn-secondary">
                    🔍 搜索服务
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="city-services-section">
    <div class="container">
        <h2 class="section-title">{{ city_info.name }}服务分类</h2>
        <p class="section-subtitle">选择您需要的服务类型，查看{{ city_info.name }}地区的优质商家</p>
        
        <div class="services-grid">
            {% for service_name, service_info in config.SERVICES.items() %}
            {% set service_shops = city_services.get(service_name, []) %}
            <div class="service-card">
                <div class="service-header">
                    <div class="service-icon">{{ service_info.icon }}</div>
                    <h3 class="service-name">{{ service_name }}</h3>
                </div>
                <p class="service-description">
                    {{ city_info.name }}地区专业{{ service_name }}
                </p>
                <div class="service-stats">
                    <span class="service-count">{{ service_shops|length }} 个商家</span>
                    <span class="service-quality">⭐ 优质认证</span>
                </div>
                <a href="/{{ city_code }}/{{ service_name.replace('服务', '').lower() }}.html" class="service-link-btn">
                    查看详情 →
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Featured Shops Section -->
{% if featured_shops %}
<section class="featured-shops-section">
    <div class="container">
        <h2 class="section-title">{{ city_info.name }}推荐商家</h2>
        <p class="section-subtitle">精选{{ city_info.name }}地区的优质服务提供者</p>
        
        <div class="shops-grid">
            {% for shop in featured_shops %}
            <div class="shop-card">
                <div class="shop-header">
                    <h3 class="shop-name">{{ shop.name }}</h3>
                    <span class="shop-type">
                        {% if shop.type == 'channel' %}
                        📢 频道
                        {% elif shop.type == 'group' %}
                        👥 群组
                        {% else %}
                        🏪 商家
                        {% endif %}
                    </span>
                </div>
                
                <div class="shop-category">
                    <span class="category-badge">{{ shop.category or '服务' }}</span>
                </div>
                
                {% if shop.description %}
                <p class="shop-description">{{ shop.description[:100] }}{% if shop.description|length > 100 %}...{% endif %}</p>
                {% endif %}
                
                <div class="shop-details">
                    {% if shop.address %}
                    <div class="detail-item">
                        <span class="detail-icon">📍</span>
                        <span class="detail-text">{{ shop.address }}</span>
                    </div>
                    {% endif %}
                    
                    <div class="detail-item">
                        <span class="detail-icon">🕒</span>
                        <span class="detail-text">24小时在线</span>
                    </div>
                </div>
                
                <div class="shop-actions">
                    <a href="/merchant/{{ shop.id }}.html" class="btn btn-outline">
                        查看详情
                    </a>
                    <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}" class="btn btn-primary" target="_blank">
                        获取联系方式
                    </a>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <div class="section-footer">
            <a href="/search.html?city={{ city_code }}" class="btn btn-secondary">
                查看更多{{ city_info.name }}商家 →
            </a>
        </div>
    </div>
</section>
{% endif %}

<!-- Other Cities Section -->
<section class="other-cities-section">
    <div class="container">
        <h2 class="section-title">其他热门城市</h2>
        <p class="section-subtitle">探索马来西亚其他地区的优质服务</p>
        
        <div class="cities-grid">
            {% for other_city_code, other_city_info in config.CITIES.items() %}
            {% if other_city_code != city_code %}
            <div class="city-card">
                <div class="city-header">
                    <h3 class="city-name">{{ other_city_info.name }}</h3>
                    <span class="city-code">{{ other_city_info.code }}</span>
                </div>
                <p class="city-description">{{ other_city_info.description }}</p>
                
                <div class="city-services">
                    {% for service_name in config.SERVICES.keys() %}
                    <a href="/{{ other_city_code }}/{{ service_name.replace('服务', '').lower() }}.html" class="service-link">
                        {{ config.SERVICES[service_name].icon }} {{ service_name }}
                    </a>
                    {% endfor %}
                </div>
                
                <div class="city-footer">
                    <a href="/{{ other_city_code }}/" class="btn btn-outline">
                        进入{{ other_city_info.name }} →
                    </a>
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title">开始您在{{ city_info.name }}的服务体验</h2>
            <p class="cta-description">
                通过走马探花Telegram机器人，安全便捷地获取{{ city_info.name }}地区的优质服务信息
            </p>
            <div class="cta-actions">
                <a href="https://t.me/{{ config.TELEGRAM_BOT_USERNAME }}" class="btn btn-primary btn-large" target="_blank">
                    📱 立即联系机器人
                </a>
            </div>
        </div>
    </div>
</section>

<!-- SEO Content -->
<section class="seo-content">
    <div class="container">
        <div class="seo-text">
            <h2>{{ city_info.name }}服务平台介绍</h2>
            <p>
                走马探花{{ city_info.name }}服务平台是专为{{ city_info.name }}地区用户打造的优质服务信息平台。
                我们精心筛选了{{ city_info.name }}地区的专业服务提供者，涵盖多种服务类型，
                确保每一位用户都能在{{ city_info.name }}找到满意的服务。
            </p>
            <p>
                {{ city_info.name }}作为马来西亚的重要城市，拥有丰富的服务资源和完善的基础设施。
                通过走马探花平台，您可以安全、便捷地获取{{ city_info.name }}地区各类服务的详细信息和联系方式。
                我们的Telegram机器人提供24小时在线服务，确保您随时都能获得所需的帮助和支持。
            </p>
            <p>
                选择走马探花{{ city_info.name }}服务平台，选择专业、安全、可靠的服务体验。
                立即联系我们的机器人，开始您在{{ city_info.name }}的优质服务之旅。
            </p>
        </div>
    </div>
</section>
{% endblock %}
