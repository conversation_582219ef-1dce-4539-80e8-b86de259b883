<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 樱花主题城市英雄区域 - 功能演示</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }

        :root {
            --primary-color: #ff69b4;
            --primary-hover: #ff1493;
            --primary-light: #ffb6c1;
            --primary-50: #fef7f7;
            --primary-100: #fce7e7;
            --primary-600: #e91e63;
            --sakura-gradient: linear-gradient(135deg, #ff69b4 0%, #ffb6c1 50%, #ffc0cb 100%);
            --sakura-light-gradient: linear-gradient(135deg, #fef7f7 0%, #fce7e7 100%);
            --sakura-glow: 0 0 20px rgba(255, 105, 180, 0.3);
            --white: #ffffff;
            --light-color: #f8fafc;
            --text-primary: #0f172a;
            --text-secondary: #64748b;
            --text-muted: #94a3b8;
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --space-sm: 0.5rem; --space-lg: 1rem; --space-xl: 1.25rem; --space-2xl: 1.5rem; --space-3xl: 2rem; --space-4xl: 3rem; --space-5xl: 4rem;
            --text-sm: 0.875rem; --text-base: 1rem; --text-lg: 1.125rem; --text-xl: 1.25rem; --text-2xl: 1.5rem; --text-3xl: 1.875rem; --text-4xl: 2.25rem; --text-6xl: 3.75rem;
            --border-radius: 8px; --border-radius-lg: 16px; --border-radius-xl: 20px; --border-radius-full: 9999px;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: var(--light-color);
            scroll-behavior: smooth;
        }

        .container { max-width: 1200px; margin: 0 auto; padding: 0 var(--space-lg); }

        /* 导航菜单 */
        .demo-nav {
            position: fixed; top: 20px; right: 20px;
            background: var(--white); border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-lg); padding: var(--space-lg); z-index: 1000;
            border: 2px solid var(--primary-100);
        }
        .demo-nav ul { list-style: none; }
        .demo-nav li { margin-bottom: var(--space-sm); }
        .demo-nav a {
            display: block; padding: var(--space-sm) var(--space-lg);
            color: var(--text-secondary); text-decoration: none;
            border-radius: var(--border-radius); transition: all 0.3s ease;
            font-size: var(--text-sm); font-weight: 500;
        }
        .demo-nav a:hover { background: var(--primary-50); color: var(--primary-color); }
        .demo-nav a.active { background: var(--primary-color); color: var(--white); }

        /* 页面头部 */
        .demo-header {
            background: var(--sakura-light-gradient);
            padding: var(--space-4xl) 0; text-align: center;
            border-bottom: 1px solid var(--primary-100);
        }
        .demo-title {
            font-size: var(--text-4xl); font-weight: 800;
            background: var(--sakura-gradient);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent;
            margin-bottom: var(--space-lg);
        }
        .demo-subtitle {
            font-size: var(--text-lg); color: var(--text-secondary);
            max-width: 600px; margin: 0 auto;
        }

        /* 通用按钮样式 */
        .btn {
            display: inline-flex; align-items: center; gap: var(--space-sm);
            padding: var(--space-sm) var(--space-2xl); border-radius: var(--border-radius);
            font-weight: 600; text-decoration: none; transition: all 0.3s ease;
            border: none; cursor: pointer; font-size: var(--text-base);
        }
        .btn-primary {
            background: var(--sakura-gradient); color: var(--white);
            box-shadow: var(--sakura-glow);
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-600) 100%);
            transform: translateY(-2px); box-shadow: 0 8px 25px rgba(255, 105, 180, 0.4);
        }
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2); color: var(--white);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .btn-secondary:hover { background: rgba(255, 255, 255, 0.3); transform: translateY(-2px); }
        .btn-outline {
            background: transparent; color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }
        .btn-outline:hover {
            background: var(--primary-color); color: var(--white);
            transform: translateY(-2px); box-shadow: var(--sakura-glow);
        }

        /* 动画 */
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(30px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes slideInLeft { from { opacity: 0; transform: translateX(-30px); } to { opacity: 1; transform: translateX(0); } }
        @keyframes slideInRight { from { opacity: 0; transform: translateX(30px); } to { opacity: 1; transform: translateX(0); } }
        @keyframes scaleIn { from { opacity: 0; transform: scale(0.9); } to { opacity: 1; transform: scale(1); } }
        @keyframes float { 0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-10px) rotate(5deg); } }

        /* 变体区域样式 */
        .variant-section { margin-bottom: var(--space-5xl); }
        .variant-header {
            background: var(--sakura-light-gradient); padding: var(--space-4xl) 0;
            text-align: center; position: relative;
        }
        .variant-header::before {
            content: '🌸'; position: absolute; top: 20px; left: 50%;
            transform: translateX(-50%); font-size: 2rem; opacity: 0.2;
        }

        /* 变体1样式 */
        .city-hero-v1 {
            background: var(--sakura-gradient); color: var(--white);
            padding: var(--space-5xl) 0; text-align: center;
            position: relative; overflow: hidden;
        }
        .city-hero-v1::before {
            content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.15) 0%, transparent 50%);
            pointer-events: none;
        }
        .city-hero-v1::after {
            content: '🌸'; position: absolute; top: 30px; right: 50px;
            font-size: 3rem; opacity: 0.2; animation: float 4s ease-in-out infinite;
        }
        .city-hero-content-v1 { position: relative; z-index: 1; animation: fadeInUp 0.8s ease-out; }
        .city-badge-v1 {
            display: inline-flex; align-items: center; gap: var(--space-sm);
            background: rgba(255, 255, 255, 0.2); padding: var(--space-sm) var(--space-xl);
            border-radius: var(--border-radius-full); margin-bottom: var(--space-2xl);
            backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3);
            animation: fadeInUp 0.8s ease-out 0.2s both; transition: all 0.3s ease;
        }
        .city-badge-v1:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px rgba(255, 105, 180, 0.3);
        }
        .city-title-v1 {
            font-size: var(--text-6xl); font-weight: 800; margin-bottom: var(--space-lg);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); animation: fadeInUp 0.8s ease-out 0.4s both;
        }
        .city-subtitle-v1 {
            font-size: var(--text-xl); font-weight: 500; margin-bottom: var(--space-2xl);
            opacity: 0.95; max-width: 700px; margin-left: auto; margin-right: auto;
            animation: fadeInUp 0.8s ease-out 0.6s both;
        }
        .city-description-v1 {
            font-size: var(--text-lg); line-height: 1.6; margin-bottom: var(--space-4xl);
            opacity: 0.9; max-width: 600px; margin-left: auto; margin-right: auto;
            animation: fadeInUp 0.8s ease-out 0.8s both;
        }
        .city-stats-v1 {
            display: flex; justify-content: center; gap: var(--space-4xl);
            margin-bottom: var(--space-4xl); animation: fadeInUp 0.8s ease-out 1s both;
        }
        .stat-item-v1 { text-align: center; transition: transform 0.3s ease; position: relative; }
        .stat-item-v1:hover { transform: translateY(-8px); }
        .stat-item-v1::before {
            content: '🌸'; position: absolute; top: -10px; right: -10px;
            font-size: 0.8rem; opacity: 0; transition: opacity 0.3s ease;
        }
        .stat-item-v1:hover::before { opacity: 0.7; }
        .stat-number-v1 {
            font-size: var(--text-4xl); font-weight: 800; display: block; margin-bottom: var(--space-sm);
            background: linear-gradient(45deg, #ffffff, #ffc0cb);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent;
        }
        .stat-label-v1 {
            font-size: var(--text-sm); opacity: 0.85; font-weight: 500;
            text-transform: uppercase; letter-spacing: 0.05em;
        }
        .city-actions-v1 {
            display: flex; justify-content: center; gap: var(--space-lg);
            flex-wrap: wrap; animation: fadeInUp 0.8s ease-out 1.2s both;
        }

        /* 变体2样式 */
        .city-hero-v2 {
            background: var(--sakura-light-gradient); padding: var(--space-5xl) 0;
            min-height: 80vh; display: flex; align-items: center;
        }
        .city-hero-card-v2 {
            background: var(--white); border-radius: var(--border-radius-xl);
            padding: var(--space-5xl); box-shadow: var(--shadow-xl);
            text-align: center; position: relative; overflow: hidden;
            animation: scaleIn 0.8s ease-out;
        }
        .city-hero-card-v2::before {
            content: ''; position: absolute; top: 0; left: 0; right: 0;
            height: 4px; background: var(--sakura-gradient);
        }
        .city-hero-card-v2::after {
            content: '🌸'; position: absolute; top: 20px; right: 20px;
            font-size: 1.5rem; opacity: 0.3; animation: float 3s ease-in-out infinite;
        }
        .city-badge-v2 {
            display: inline-flex; align-items: center; gap: var(--space-lg);
            background: var(--sakura-gradient); color: var(--white);
            padding: var(--space-lg) var(--space-2xl); border-radius: var(--border-radius-full);
            margin-bottom: var(--space-2xl); box-shadow: var(--sakura-glow);
            animation: slideInLeft 0.8s ease-out 0.2s both;
        }
        .city-badge-v2:hover { transform: translateY(-3px) scale(1.05); }
        .badge-icon-v2 {
            font-size: 1.5rem; background: rgba(255, 255, 255, 0.2);
            width: 2.5rem; height: 2.5rem; border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
        }
        .city-title-v2 {
            font-size: var(--text-6xl); font-weight: 800; margin-bottom: var(--space-lg);
            background: var(--sakura-gradient); -webkit-background-clip: text;
            -webkit-text-fill-color: transparent; animation: slideInRight 0.8s ease-out 0.4s both;
        }
        .city-subtitle-v2 {
            font-size: var(--text-xl); font-weight: 600; color: var(--text-secondary);
            margin-bottom: var(--space-2xl); animation: fadeInUp 0.8s ease-out 0.6s both;
        }
        .city-description-v2 {
            font-size: var(--text-lg); color: var(--text-muted);
            margin-bottom: var(--space-4xl); animation: fadeInUp 0.8s ease-out 0.8s both;
        }
        .city-stats-v2 {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--space-2xl); margin-bottom: var(--space-4xl);
            animation: fadeInUp 0.8s ease-out 1s both;
        }
        .stat-card-v2 {
            background: var(--primary-50); padding: var(--space-2xl);
            border-radius: var(--border-radius-lg); transition: all 0.3s ease;
            border: 2px solid transparent; position: relative;
        }
        .stat-card-v2:hover {
            transform: translateY(-5px); box-shadow: var(--shadow-lg);
            border-color: var(--primary-color); background: var(--primary-100);
        }
        .stat-card-v2::before {
            content: '🌸'; position: absolute; top: 5px; right: 5px;
            font-size: 0.8rem; opacity: 0; transition: opacity 0.3s ease;
        }
        .stat-card-v2:hover::before { opacity: 0.6; }
        .stat-icon-v2 { font-size: 2rem; margin-bottom: var(--space-sm); display: block; }
        .stat-number-v2 {
            font-size: var(--text-3xl); font-weight: 800; color: var(--primary-color);
            display: block; margin-bottom: var(--space-sm);
        }
        .stat-label-v2 { font-size: var(--text-sm); color: var(--text-secondary); font-weight: 600; }
        .city-actions-v2 {
            display: flex; justify-content: center; gap: var(--space-lg);
            flex-wrap: wrap; animation: fadeInUp 0.8s ease-out 1.2s both;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .demo-nav { top: 10px; right: 10px; padding: var(--space-sm); }
            .demo-nav a { padding: var(--space-sm); font-size: var(--text-xs); }
            .demo-title { font-size: var(--text-3xl); }
            .city-hero-v1 { padding: var(--space-4xl) 0; }
            .city-title-v1, .city-title-v2 { font-size: var(--text-4xl); }
            .city-subtitle-v1, .city-subtitle-v2 { font-size: var(--text-lg); }
            .city-stats-v1 { gap: var(--space-2xl); flex-wrap: wrap; }
            .city-actions-v1, .city-actions-v2 { flex-direction: column; align-items: center; }
            .city-actions-v1 .btn, .city-actions-v2 .btn { width: 200px; }

            /* 变体3移动端适配 */
            section[style*="grid-template-columns: 1fr 400px"] {
                display: block !important;
            }
            section[style*="grid-template-columns: 1fr 400px"] > div > div {
                display: block !important;
                gap: var(--space-3xl) !important;
            }
            section[style*="grid-template-columns: 1fr 400px"] > div > div > div:last-child {
                margin-top: var(--space-4xl);
                max-width: none;
            }

            /* 变体4移动端适配 */
            section[style*="min-height: 100vh"] {
                min-height: 80vh !important;
                padding: var(--space-4xl) 0 !important;
            }
            section[style*="min-height: 100vh"] div[style*="font-size: 4rem"] {
                display: none;
            }
            section[style*="min-height: 100vh"] div[style*="font-size: 3rem"] {
                display: none;
            }

            /* 变体5移动端适配 */
            section[style*="border-top: 3px solid"] div[style*="gap: var(--space-4xl)"] {
                flex-direction: column !important;
                gap: var(--space-2xl) !important;
            }
            section[style*="border-top: 3px solid"] div[style*="width: 1px"] {
                display: none;
            }
        }
    </style>
    <script>
        // 确保平滑滚动和导航功能正常工作
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有导航链接添加点击事件
            const navLinks = document.querySelectorAll('.demo-nav a[href^="#"]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        // 平滑滚动到目标元素
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });

                        // 更新导航链接的活跃状态
                        navLinks.forEach(navLink => navLink.classList.remove('active'));
                        this.classList.add('active');
                    }
                });
            });

            // 监听滚动事件，更新导航状态
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('.variant-section');
                const scrollPos = window.scrollY + 100;

                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionHeight = section.offsetHeight;
                    const sectionId = section.getAttribute('id');

                    if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                        navLinks.forEach(link => link.classList.remove('active'));
                        const activeLink = document.querySelector(`.demo-nav a[href="#${sectionId}"]`);
                        if (activeLink) {
                            activeLink.classList.add('active');
                        }
                    }
                });
            });
        });
    </script>
</head>
<body>
    <!-- 导航菜单 -->
    <nav class="demo-nav">
        <ul>
            <li><a href="#variant1">🌸 现代简约</a></li>
            <li><a href="#variant2">🌸 卡片式</a></li>
            <li><a href="#variant3">🌸 分屏布局</a></li>
            <li><a href="#variant4">🌸 全屏沉浸</a></li>
            <li><a href="#variant5">🌸 极简主义</a></li>
        </ul>
    </nav>

    <!-- 页面头部 -->
    <div class="demo-header">
        <div class="container">
            <h1 class="demo-title">🌸 樱花主题城市英雄区域</h1>
            <p class="demo-subtitle">
                5种优雅的樱花粉色设计变体，完美配合您的樱花logo主题，
                保持现代感的同时融入温柔浪漫的樱花元素。
            </p>
        </div>
    </div>

    <!-- 变体1：樱花现代简约风格 -->
    <section class="variant-section" id="variant1">
        <div class="variant-header">
            <div class="container">
                <h2 style="color: var(--primary-color); font-size: var(--text-2xl); font-weight: 700; margin-bottom: var(--space-sm);">
                    🌸 变体1：樱花现代简约风格
                </h2>
                <p style="color: var(--text-secondary);">清洁的设计，强调内容层次，使用樱花渐变和优雅的动画效果。</p>
            </div>
        </div>
        <section class="city-hero-v1">
            <div class="container">
                <div class="city-hero-content-v1">
                    <div class="city-badge-v1">
                        <span>📍</span><span style="font-weight: 700; font-size: var(--text-sm); letter-spacing: 0.1em;">KL</span>
                    </div>
                    <h1 class="city-title-v1">吉隆坡</h1>
                    <h2 class="city-subtitle-v1">提供吉隆坡按摩、下水、B2B等真实服务信息</h2>
                    <p class="city-description-v1">走马探花为您提供吉隆坡地区最全面的优质服务信息<br>通过Telegram机器人获取详细联系方式和服务详情</p>
                    <div class="city-stats-v1">
                        <div class="stat-item-v1"><div class="stat-number-v1">15</div><div class="stat-label-v1">优质商家</div></div>
                        <div class="stat-item-v1"><div class="stat-number-v1">3</div><div class="stat-label-v1">服务分类</div></div>
                        <div class="stat-item-v1"><div class="stat-number-v1">24/7</div><div class="stat-label-v1">在线服务</div></div>
                    </div>
                    <div class="city-actions-v1">
                        <a href="#" class="btn btn-primary">📱 联系机器人</a>
                        <a href="#" class="btn btn-secondary">🔍 搜索服务</a>
                    </div>
                </div>
            </div>
        </section>
    </section>

    <!-- 变体2：樱花卡片式设计 -->
    <section class="variant-section" id="variant2">
        <div class="variant-header">
            <div class="container">
                <h2 style="color: var(--primary-color); font-size: var(--text-2xl); font-weight: 700; margin-bottom: var(--space-sm);">
                    🌸 变体2：樱花卡片式设计
                </h2>
                <p style="color: var(--text-secondary);">将内容包装在优雅的卡片中，营造层次感和深度，使用樱花粉色装饰边框。</p>
            </div>
        </div>
        <section class="city-hero-v2">
            <div class="container">
                <div class="city-hero-card-v2">
                    <div class="city-badge-v2">
                        <div class="badge-icon-v2">📍</div>
                        <span style="font-weight: 700; font-size: var(--text-lg); letter-spacing: 0.1em;">KL</span>
                    </div>
                    <h1 class="city-title-v2">吉隆坡</h1>
                    <h2 class="city-subtitle-v2">提供吉隆坡按摩、下水、B2B等真实服务信息</h2>
                    <p class="city-description-v2">走马探花为您提供吉隆坡地区最全面的优质服务信息，通过Telegram机器人获取详细联系方式和服务详情</p>
                    <div class="city-stats-v2">
                        <div class="stat-card-v2"><div class="stat-icon-v2">🏪</div><div class="stat-number-v2">15</div><div class="stat-label-v2">优质商家</div></div>
                        <div class="stat-card-v2"><div class="stat-icon-v2">⚡</div><div class="stat-number-v2">3</div><div class="stat-label-v2">服务分类</div></div>
                        <div class="stat-card-v2"><div class="stat-icon-v2">🕒</div><div class="stat-number-v2">24/7</div><div class="stat-label-v2">在线服务</div></div>
                    </div>
                    <div class="city-actions-v2">
                        <a href="#" class="btn btn-primary">📱 联系机器人</a>
                        <a href="#" class="btn btn-outline">🔍 搜索服务</a>
                    </div>
                </div>
            </div>
        </section>
    </section>

    <!-- 变体3：樱花分屏布局设计 -->
    <section class="variant-section" id="variant3">
        <div class="variant-header">
            <div class="container">
                <h2 style="color: var(--primary-color); font-size: var(--text-2xl); font-weight: 700; margin-bottom: var(--space-sm);">
                    🌸 变体3：樱花分屏布局设计
                </h2>
                <p style="color: var(--text-secondary);">左右分屏布局，左侧内容右侧统计，使用樱花粉色圆形统计图标。</p>
            </div>
        </div>
        <section style="background: var(--white); padding: var(--space-5xl) 0; min-height: 80vh; display: flex; align-items: center;">
            <div class="container">
                <div style="display: grid; grid-template-columns: 1fr 400px; gap: var(--space-5xl); align-items: center;">
                    <div style="animation: slideInLeft 0.8s ease-out;">
                        <div style="display: inline-flex; align-items: center; gap: var(--space-sm); background: var(--primary-100); color: var(--primary-color); padding: var(--space-sm) var(--space-lg); border-radius: var(--border-radius-full); margin-bottom: var(--space-2xl); font-weight: 600;">
                            <span>📍</span><span style="font-weight: 700; font-size: var(--text-sm); letter-spacing: 0.1em;">KL</span>
                        </div>
                        <h1 style="font-size: var(--text-6xl); font-weight: 800; margin-bottom: var(--space-lg); color: var(--text-primary);">吉隆坡</h1>
                        <h2 style="font-size: var(--text-2xl); font-weight: 600; color: var(--text-secondary); margin-bottom: var(--space-2xl); line-height: 1.4;">提供吉隆坡按摩、下水、B2B等真实服务信息</h2>
                        <p style="font-size: var(--text-lg); line-height: 1.6; color: var(--text-muted); margin-bottom: var(--space-4xl);">走马探花为您提供吉隆坡地区最全面的优质服务信息，通过Telegram机器人获取详细联系方式和服务详情</p>
                        <div style="display: flex; gap: var(--space-lg);">
                            <a href="#" class="btn btn-primary">📱 联系机器人</a>
                            <a href="#" class="btn btn-secondary">🔍 搜索服务</a>
                        </div>
                    </div>
                    <div style="background: var(--white); border-radius: var(--border-radius-xl); padding: var(--space-4xl); box-shadow: var(--shadow-xl); border: 2px solid var(--primary-100); animation: slideInRight 0.8s ease-out 0.4s both;">
                        <div style="text-align: center; margin-bottom: var(--space-3xl);">
                            <h3 style="font-size: var(--text-2xl); font-weight: 700; color: var(--primary-color);">🌸 服务概览</h3>
                        </div>
                        <div style="display: flex; flex-direction: column; gap: var(--space-3xl);">
                            <div style="display: flex; align-items: center; gap: var(--space-lg);">
                                <div style="width: 80px; height: 80px; border-radius: 50%; background: var(--sakura-gradient); display: flex; align-items: center; justify-content: center; color: var(--white); font-weight: 800; font-size: var(--text-lg); box-shadow: var(--sakura-glow);">15</div>
                                <div style="font-size: var(--text-lg); font-weight: 600; color: var(--text-primary);">优质商家</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: var(--space-lg);">
                                <div style="width: 80px; height: 80px; border-radius: 50%; background: var(--sakura-gradient); display: flex; align-items: center; justify-content: center; color: var(--white); font-weight: 800; font-size: var(--text-lg); box-shadow: var(--sakura-glow);">3</div>
                                <div style="font-size: var(--text-lg); font-weight: 600; color: var(--text-primary);">服务分类</div>
                            </div>
                            <div style="display: flex; align-items: center; gap: var(--space-lg);">
                                <div style="width: 80px; height: 80px; border-radius: 50%; background: var(--sakura-gradient); display: flex; align-items: center; justify-content: center; color: var(--white); font-weight: 800; font-size: var(--text-lg); box-shadow: var(--sakura-glow);">24/7</div>
                                <div style="font-size: var(--text-lg); font-weight: 600; color: var(--text-primary);">在线服务</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </section>

    <!-- 变体4：樱花全屏沉浸式 -->
    <section class="variant-section" id="variant4">
        <div class="variant-header">
            <div class="container">
                <h2 style="color: var(--primary-color); font-size: var(--text-2xl); font-weight: 700; margin-bottom: var(--space-sm);">
                    🌸 变体4：樱花全屏沉浸式
                </h2>
                <p style="color: var(--text-secondary);">全屏高度设计，樱花多彩渐变背景，营造浪漫沉浸式体验。</p>
            </div>
        </div>
        <section style="background: linear-gradient(45deg, var(--primary-color), var(--primary-light), #ffc0cb); color: var(--white); padding: var(--space-5xl) 0; min-height: 100vh; display: flex; align-items: center; text-align: center; position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 20% 30%, rgba(255,255,255,0.2) 0%, transparent 40%), radial-gradient(circle at 80% 70%, rgba(255,255,255,0.15) 0%, transparent 40%); pointer-events: none;"></div>
            <div style="position: absolute; top: 50px; right: 100px; font-size: 4rem; opacity: 0.1; animation: float 5s ease-in-out infinite;">🌸</div>
            <div style="position: absolute; bottom: 100px; left: 80px; font-size: 3rem; opacity: 0.15; animation: float 4s ease-in-out infinite reverse;">🌸</div>
            <div class="container">
                <div style="position: relative; z-index: 1; animation: fadeInUp 0.8s ease-out;">
                    <div style="display: inline-flex; align-items: center; gap: var(--space-sm); background: rgba(255, 255, 255, 0.25); padding: var(--space-lg) var(--space-2xl); border-radius: var(--border-radius-full); margin-bottom: var(--space-2xl); backdrop-filter: blur(15px); border: 1px solid rgba(255, 255, 255, 0.3);">
                        <span style="font-size: 1.5rem;">📍</span><span style="font-weight: 700; font-size: var(--text-lg); letter-spacing: 0.1em;">KL</span>
                    </div>
                    <h1 style="font-size: var(--text-6xl); font-weight: 800; margin-bottom: var(--space-lg); text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);">吉隆坡</h1>
                    <h2 style="font-size: var(--text-2xl); font-weight: 500; margin-bottom: var(--space-2xl); opacity: 0.95; max-width: 800px; margin-left: auto; margin-right: auto;">提供吉隆坡按摩、下水、B2B等真实服务信息</h2>
                    <p style="font-size: var(--text-lg); line-height: 1.6; margin-bottom: var(--space-4xl); opacity: 0.9; max-width: 600px; margin-left: auto; margin-right: auto;">走马探花为您提供吉隆坡地区最全面的优质服务信息<br>通过Telegram机器人获取详细联系方式和服务详情</p>
                    <div style="display: flex; justify-content: center; gap: var(--space-5xl); margin-bottom: var(--space-4xl); flex-wrap: wrap;">
                        <div style="background: rgba(255, 255, 255, 0.2); padding: var(--space-3xl); border-radius: var(--border-radius-xl); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; min-width: 150px;">
                            <div style="font-size: var(--text-4xl); font-weight: 800; margin-bottom: var(--space-sm);">15</div>
                            <div style="font-size: var(--text-sm); opacity: 0.9; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">优质商家</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.2); padding: var(--space-3xl); border-radius: var(--border-radius-xl); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; min-width: 150px;">
                            <div style="font-size: var(--text-4xl); font-weight: 800; margin-bottom: var(--space-sm);">3</div>
                            <div style="font-size: var(--text-sm); opacity: 0.9; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">服务分类</div>
                        </div>
                        <div style="background: rgba(255, 255, 255, 0.2); padding: var(--space-3xl); border-radius: var(--border-radius-xl); backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3); text-align: center; min-width: 150px;">
                            <div style="font-size: var(--text-4xl); font-weight: 800; margin-bottom: var(--space-sm);">24/7</div>
                            <div style="font-size: var(--text-sm); opacity: 0.9; font-weight: 500; text-transform: uppercase; letter-spacing: 0.05em;">在线服务</div>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: center; gap: var(--space-lg); flex-wrap: wrap;">
                        <a href="#" class="btn btn-primary" style="background: rgba(255, 255, 255, 0.9); color: var(--primary-color); box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);">📱 联系机器人</a>
                        <a href="#" class="btn btn-secondary">🔍 搜索服务</a>
                    </div>
                </div>
            </div>
        </section>
    </section>

    <!-- 变体5：樱花极简主义 -->
    <section class="variant-section" id="variant5">
        <div class="variant-header">
            <div class="container">
                <h2 style="color: var(--primary-color); font-size: var(--text-2xl); font-weight: 700; margin-bottom: var(--space-sm);">
                    🌸 变体5：樱花极简主义
                </h2>
                <p style="color: var(--text-secondary);">去除多余装饰，专注内容本身，使用樱花粉色线条和留白营造优雅感。</p>
            </div>
        </div>
        <section style="background: var(--white); padding: var(--space-5xl) 0; min-height: 80vh; display: flex; align-items: center; border-top: 3px solid var(--primary-color); border-bottom: 3px solid var(--primary-color);">
            <div class="container">
                <div style="text-align: center; max-width: 800px; margin: 0 auto; animation: fadeInUp 0.8s ease-out;">
                    <div style="display: inline-flex; align-items: center; gap: var(--space-sm); color: var(--primary-color); font-weight: 600; margin-bottom: var(--space-3xl); padding: var(--space-sm) var(--space-lg); border: 2px solid var(--primary-color); border-radius: var(--border-radius-full);">
                        <span>📍</span><span style="font-weight: 700; font-size: var(--text-sm); letter-spacing: 0.1em;">KL</span>
                    </div>
                    <h1 style="font-size: var(--text-6xl); font-weight: 800; margin-bottom: var(--space-lg); color: var(--text-primary); position: relative;">
                        吉隆坡
                        <div style="position: absolute; top: -10px; right: -20px; font-size: 1rem; opacity: 0.4;">🌸</div>
                    </h1>
                    <h2 style="font-size: var(--text-2xl); font-weight: 400; color: var(--text-secondary); margin-bottom: var(--space-4xl); line-height: 1.4;">提供吉隆坡按摩、下水、B2B等真实服务信息</h2>
                    <p style="font-size: var(--text-lg); line-height: 1.8; color: var(--text-muted); margin-bottom: var(--space-5xl); max-width: 600px; margin-left: auto; margin-right: auto;">走马探花为您提供吉隆坡地区最全面的优质服务信息，通过Telegram机器人获取详细联系方式和服务详情</p>
                    <div style="display: flex; justify-content: center; align-items: center; gap: var(--space-4xl); margin-bottom: var(--space-5xl); padding: var(--space-3xl) 0; border-top: 1px solid var(--primary-200); border-bottom: 1px solid var(--primary-200);">
                        <div style="text-align: center;">
                            <div style="font-size: var(--text-3xl); font-weight: 700; color: var(--primary-color); margin-bottom: var(--space-sm);">15</div>
                            <div style="font-size: var(--text-sm); color: var(--text-muted); font-weight: 500;">优质商家</div>
                        </div>
                        <div style="width: 1px; height: 40px; background: var(--primary-200);"></div>
                        <div style="text-align: center;">
                            <div style="font-size: var(--text-3xl); font-weight: 700; color: var(--primary-color); margin-bottom: var(--space-sm);">3</div>
                            <div style="font-size: var(--text-sm); color: var(--text-muted); font-weight: 500;">服务分类</div>
                        </div>
                        <div style="width: 1px; height: 40px; background: var(--primary-200);"></div>
                        <div style="text-align: center;">
                            <div style="font-size: var(--text-3xl); font-weight: 700; color: var(--primary-color); margin-bottom: var(--space-sm);">24/7</div>
                            <div style="font-size: var(--text-sm); color: var(--text-muted); font-weight: 500;">在线服务</div>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: center; gap: var(--space-lg);">
                        <a href="#" class="btn btn-primary">📱 联系机器人</a>
                        <a href="#" class="btn btn-outline">🔍 搜索服务</a>
                    </div>
                </div>
            </div>
        </section>
    </section>

</body>
</html>
